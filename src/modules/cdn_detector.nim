##[
  CDN检测模块
  通过多种方式检测目标是否使用CDN
]##

import asyncdispatch, httpclient, json, strutils, sequtils, net, os, tables, osproc, streams
import ../core/[logger, config]

type
  CDNResult* = object
    hasCDN*: bool
    cdnProvider*: string
    realIPs*: seq[string]
    cdnIPs*: seq[string]
    confidence*: float

  PingLocation* = object
    country*: string
    city*: string
    ip*: string

# CDN厂商特征库
const CDN_SIGNATURES = {
  "cloudflare": @["cloudflare", "cf-ray", "__cfduid"],
  "akamai": @["akamai", "akamaihd", "edgesuite"],
  "fastly": @["fastly", "fastlylb"],
  "amazon": @["cloudfront", "amazonaws"],
  "maxcdn": @["maxcdn", "bootstrapcdn"],
  "incapsula": @["incap_ses", "visid_incap"],
  "sucuri": @["sucuri", "cloudproxy"],
  "keycdn": @["keycdn"],
  "stackpath": @["stackpath", "netdna"],
  "aliyun": @["kunlun", "aliyun", "alicdn"],
  "tencent": @["qcloud", "tencent", "myqcloud"],
  "baidu": @["bcebos", "baidubce", "bdstatic"],
  "qiniu": @["qiniudn", "clouddn"],
  "upyun": @["upyun", "upaiyun"]
}.toTable()

proc resolveMultipleIPs(domain: string): Future[seq[string]] {.async.} =
  ## 解析域名获取多个IP地址 (简化版本)
  try:
    # 使用nslookup命令获取IP
    let process = startProcess("nslookup", args=[domain], options={poUsePath})
    let outputStream = process.outputStream
    let output = outputStream.readAll()
    process.close()

    result = @[]
    for line in output.splitLines():
      if "Address:" in line and not line.contains("#"):
        let parts = line.split("Address:")
        if parts.len > 1:
          let ip = parts[1].strip()
          if ip.len > 0 and ip notin result:
            result.add(ip)

    if result.len > 0:
      logDebug("Resolved IPs for $1: $2" % [domain, result.join(", ")])
    else:
      logWarn("No IPs resolved for: " & domain)
  except:
    logError("Failed to resolve domain: " & domain)
    result = @[]

proc checkCDNHeaders(domain: string): Future[tuple[isCDN: bool, provider: string]] {.async.} =
  ## 通过HTTP头检测CDN
  var client = newAsyncHttpClient()
  client.headers = newHttpHeaders({"User-Agent": "Mozilla/5.0 (compatible; KaiTian/1.0)"})
  
  try:
    let response = await client.get("http://" & domain)
    let headers = response.headers
    
    # 检查CDN特征头
    for provider, signatures in CDN_SIGNATURES:
      for signature in signatures:
        for key, value in headers:
          if signature.toLower() in key.toLower() or signature.toLower() in value.toLower():
            logResult("CDN-HEADER", "$1 detected via header: $2" % [provider, key])
            return (true, provider)
    
    # 检查Server头
    if headers.hasKey("server"):
      let server = headers["server"].toLower()
      for provider, signatures in CDN_SIGNATURES:
        for signature in signatures:
          if signature in server:
            logResult("CDN-SERVER", "$1 detected via Server header" % [provider])
            return (true, provider)
    
    return (false, "")
    
  except:
    logError("Failed to check CDN headers for: " & domain)
    return (false, "")
  finally:
    client.close()

proc performMultiLocationPing(domain: string): Future[seq[PingLocation]] {.async.} =
  ## 模拟多地点ping检测
  # 这里简化实现，实际可以调用多个ping服务API
  let pingServices = @[
    "https://api.ping.pe/",
    "https://tools.keycdn.com/geo",
    "https://www.whatsmydns.net/"
  ]
  
  result = @[]
  
  # 简化版本：直接解析IP并检查地理位置差异
  let ips = await resolveMultipleIPs(domain)
  if ips.len > 3:  # 如果有多个不同IP，可能是CDN
    logResult("MULTI-IP", "Found $1 different IPs, possible CDN" % [$ips.len])
    for ip in ips:
      result.add(PingLocation(country: "Unknown", city: "Unknown", ip: ip))

proc checkCNAMERecord(domain: string): Future[bool] {.async.} =
  ## 检查CNAME记录是否指向CDN
  try:
    # 使用nslookup命令检查CNAME
    let process = startProcess("nslookup", args=["-type=CNAME", domain], options={poUsePath})
    let output = process.outputStream.readAll()
    process.close()
    
    let outputLower = output.toLower()
    
    # 检查CNAME是否指向已知CDN服务商
    for provider, signatures in CDN_SIGNATURES:
      for signature in signatures:
        if signature in outputLower:
          logResult("CDN-CNAME", "$1 detected via CNAME record" % [provider])
          return true
    
    return false
  except:
    logError("Failed to check CNAME record for: " & domain)
    return false

proc analyzeIPGeolocation(ips: seq[string]): Future[bool] {.async.} =
  ## 分析IP地理位置分布判断是否为CDN
  if ips.len < 2:
    return false
  
  # 简化版本：如果IP数量较多且分布广泛，可能是CDN
  if ips.len >= 3:
    logResult("GEO-ANALYSIS", "Multiple IPs detected, likely CDN distribution")
    return true
  
  return false

proc detectCDN*(domain: string): Future[CDNResult] {.async.} =
  ## 主CDN检测函数
  logInfo("Starting CDN detection for: " & domain)
  
  var result = CDNResult(
    hasCDN: false,
    cdnProvider: "",
    realIPs: @[],
    cdnIPs: @[],
    confidence: 0.0
  )
  
  var confidence = 0.0
  var detectedProvider = ""
  
  # 1. 解析IP地址
  let ips = await resolveMultipleIPs(domain)
  result.cdnIPs = ips
  
  # 2. 检查HTTP头
  let (headerCDN, headerProvider) = await checkCDNHeaders(domain)
  if headerCDN:
    confidence += 0.4
    detectedProvider = headerProvider
  
  # 3. 检查CNAME记录
  let cnameCDN = await checkCNAMERecord(domain)
  if cnameCDN:
    confidence += 0.3
  
  # 4. 多地点ping分析
  let pingResults = await performMultiLocationPing(domain)
  if pingResults.len > 0:
    confidence += 0.2
  
  # 5. IP地理位置分析
  let geoCDN = await analyzeIPGeolocation(ips)
  if geoCDN:
    confidence += 0.1
  
  result.hasCDN = confidence > 0.3
  result.cdnProvider = detectedProvider
  result.confidence = confidence
  
  if result.hasCDN:
    logSuccess("CDN detected: $1 (confidence: $2)" % [detectedProvider, $confidence])
  else:
    logInfo("No CDN detected (confidence: $1)" % [$confidence])
  
  return result

proc bypassCDN*(domain: string): Future[seq[string]] {.async.} =
  ## CDN绕过，尝试找到真实IP
  logInfo("Attempting CDN bypass for: " & domain)
  
  result = @[]
  
  # 方法1: 子域名扫描寻找直连IP
  let subdomains = @["mail", "ftp", "admin", "test", "dev", "staging", "origin"]
  
  for subdomain in subdomains:
    let fullDomain = subdomain & "." & domain
    try:
      let ips = await resolveMultipleIPs(fullDomain)
      for ip in ips:
        if ip notin result:
          result.add(ip)
          logResult("BYPASS-IP", "Found potential real IP via $1: $2" % [fullDomain, ip])
    except:
      continue
  
  # 方法2: 历史DNS记录查询 (将在dns_history模块中实现)
  
  return result
