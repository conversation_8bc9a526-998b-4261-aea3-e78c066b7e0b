##[
  信息收集模块
  实现证书查询、ICP备案、搜索引擎查询、WHOIS、GitHub泄露检测
]##

import asyncdispatch, httpclient, json, strutils, sequtils, osproc, streams, times
import ../core/[logger, config]
import ../modules/evasion

type
  CertInfo* = object
    issuer*: string
    subject*: string
    validFrom*: string
    validTo*: string
    san*: seq[string]  # Subject Alternative Names

  WhoisInfo* = object
    registrar*: string
    registrationDate*: string
    expirationDate*: string
    nameServers*: seq[string]
    contacts*: seq[string]

  ICPInfo* = object
    hasICP*: bool
    icpNumber*: string
    company*: string

  SearchResult* = object
    engine*: string
    results*: seq[string]

  InfoCollectionResult* = object
    domain*: string
    certInfo*: CertInfo
    whoisInfo*: WhoisInfo
    icpInfo*: ICPInfo
    searchResults*: seq[SearchResult]
    githubLeaks*: seq[string]

proc getCertificateInfo(domain: string): Future[CertInfo] {.async.} =
  ## 获取SSL证书信息
  var result = CertInfo()

  try:
    # 使用openssl命令获取证书信息
    let opensslProcess = startProcess("openssl",
      args=["s_client", "-connect", domain & ":443", "-servername", domain, "-showcerts"],
      options={poUsePath})

    # 发送quit命令并获取输出
    opensslProcess.inputStream.write("quit\n")
    opensslProcess.inputStream.close()

    let output = opensslProcess.outputStream.readAll()
    opensslProcess.close()

    # 解析证书信息
    let lines = output.splitLines()
    for i, line in lines:
      if "subject=" in line:
        result.subject = line.split("subject=")[1].strip()
      elif "issuer=" in line:
        result.issuer = line.split("issuer=")[1].strip()
      elif "notBefore=" in line:
        result.validFrom = line.split("notBefore=")[1].strip()
      elif "notAfter=" in line:
        result.validTo = line.split("notAfter=")[1].strip()
      elif "DNS:" in line:
        # 提取SAN域名
        let dnsEntries = line.split("DNS:")
        for entry in dnsEntries[1..^1]:
          let cleanEntry = entry.split(",")[0].strip()
          if cleanEntry notin result.san:
            result.san.add(cleanEntry)

    logResult("CERT-INFO", "Certificate info collected for $1" % [domain])
    logResult("CERT-ISSUER", result.issuer)
    logResult("CERT-VALID", "$1 to $2" % [result.validFrom, result.validTo])

    if result.san.len > 0:
      logResult("CERT-SAN", "Found $1 SAN domains" % [$result.san.len])
      for san in result.san:
        logResult("CERT-SAN-DOMAIN", san)

  except Exception as e:
    logWarn("Certificate info collection failed: " & e.msg)

  return result

proc getWhoisInfo(domain: string): Future[WhoisInfo] {.async.} =
  ## 获取WHOIS信息
  var result = WhoisInfo()

  try:
    let whoisProcess = startProcess("whois", args=[domain], options={poUsePath})
    let output = whoisProcess.outputStream.readAll()
    whoisProcess.close()

    let lines = output.splitLines()
    for line in lines:
      let lowerLine = line.toLower()
      if "registrar:" in lowerLine:
        result.registrar = line.split(":")[1].strip()
      elif "creation date:" in lowerLine or "registered:" in lowerLine:
        result.registrationDate = line.split(":")[1].strip()
      elif "expiry date:" in lowerLine or "expires:" in lowerLine:
        result.expirationDate = line.split(":")[1].strip()
      elif "name server:" in lowerLine:
        let ns = line.split(":")[1].strip()
        if ns notin result.nameServers:
          result.nameServers.add(ns)

    logResult("WHOIS-INFO", "WHOIS info collected for $1" % [domain])
    if result.registrar != "":
      logResult("WHOIS-REGISTRAR", result.registrar)
    if result.registrationDate != "":
      logResult("WHOIS-CREATED", result.registrationDate)
    if result.nameServers.len > 0:
      logResult("WHOIS-NS", "Found $1 name servers" % [$result.nameServers.len])

  except Exception as e:
    logWarn("WHOIS query failed: " & e.msg)

  return result

proc checkICPRecord(domain: string): Future[ICPInfo] {.async.} =
  ## 检查ICP备案信息
  var result = ICPInfo(hasICP: false)

  try:
    # 使用ICP备案查询API (简化版本)
    let icpAPIs = @[
      "http://icp.chinaz.com/" & domain,
      "https://www.beianx.cn/search/" & domain
    ]

    for api in icpAPIs:
      try:
        let curlProcess = startProcess("curl",
          args=["-s", "--connect-timeout", "10",
                "-H", "User-Agent: Mozilla/5.0 (compatible; KaiTian/1.0)",
                api],
          options={poUsePath})
        let output = curlProcess.outputStream.readAll()
        let exitCode = curlProcess.waitForExit()
        curlProcess.close()

        if exitCode == 0:
          # 简单的ICP号码检测
          if "京ICP备" in output or "沪ICP备" in output or "粤ICP备" in output:
            result.hasICP = true
            # 尝试提取ICP号码
            let icpPatterns = @["京ICP备", "沪ICP备", "粤ICP备", "ICP备"]
            for pattern in icpPatterns:
              if pattern in output:
                let parts = output.split(pattern)
                if parts.len > 1:
                  let icpPart = parts[1].split(" ")[0].split("<")[0]
                  result.icpNumber = pattern & icpPart
                  break
            break
      except:
        continue

    if result.hasICP:
      logResult("ICP-INFO", "ICP record found: $1" % [result.icpNumber])
    else:
      logResult("ICP-INFO", "No ICP record found")

  except Exception as e:
    logWarn("ICP check failed: " & e.msg)

  return result

proc searchEngineQuery(domain: string, engine: string): Future[SearchResult] {.async.} =
  ## 搜索引擎查询
  var result = SearchResult(engine: engine)

  try:
    await randomDelay()

    var searchUrl = ""
    case engine:
      of "google":
        searchUrl = "https://www.google.com/search?q=site:" & domain
      of "bing":
        searchUrl = "https://www.bing.com/search?q=site:" & domain
      of "baidu":
        searchUrl = "https://www.baidu.com/s?wd=site:" & domain
      else:
        return result

    let curlProcess = startProcess("curl",
      args=["-s", "--connect-timeout", "15",
            "-H", "User-Agent: " & getRandomUserAgent(),
            searchUrl],
      options={poUsePath})
    let output = curlProcess.outputStream.readAll()
    let exitCode = curlProcess.waitForExit()
    curlProcess.close()

    if exitCode == 0:
      # 简单的结果提取 (实际应该用更复杂的解析)
      let lines = output.splitLines()
      var resultCount = 0
      for line in lines:
        if domain in line and ("http" in line or "https" in line):
          result.results.add(line.strip())
          resultCount += 1
          if resultCount >= 10:  # 限制结果数量
            break

      logResult("SEARCH-$1" % [engine.toUpper()], "Found $1 results" % [$result.results.len])

  except Exception as e:
    logWarn("$1 search failed: $2" % [engine, e.msg])

  return result

proc checkGitHubLeaks(domain: string): Future[seq[string]] {.async.} =
  ## 检查GitHub泄露
  result = @[]

  try:
    await randomDelay()

    # GitHub搜索API (需要注意频率限制)
    let searchQueries = @[
      domain,
      "\"" & domain & "\"",
      domain.split(".")[0]  # 只搜索主域名部分
    ]

    for query in searchQueries:
      try:
        let githubUrl = "https://api.github.com/search/code?q=" & query
        let curlProcess = startProcess("curl",
          args=["-s", "--connect-timeout", "10",
                "-H", "User-Agent: " & getRandomUserAgent(),
                "-H", "Accept: application/vnd.github.v3+json",
                githubUrl],
          options={poUsePath})
        let output = curlProcess.outputStream.readAll()
        let exitCode = curlProcess.waitForExit()
        curlProcess.close()

        if exitCode == 0 and output.len > 10:
          try:
            let jsonData = parseJson(output)
            if jsonData.hasKey("items"):
              for item in jsonData["items"]:
                if item.hasKey("html_url"):
                  let url = item["html_url"].getStr()
                  if url notin result:
                    result.add(url)
          except:
            continue

        await sleepAsync(2000)  # GitHub API频率限制
      except:
        continue

    if result.len > 0:
      logResult("GITHUB-LEAKS", "Found $1 potential leaks" % [$result.len])
      for leak in result:
        logResult("GITHUB-LEAK", leak)
    else:
      logResult("GITHUB-LEAKS", "No obvious leaks found")

  except Exception as e:
    logWarn("GitHub leak check failed: " & e.msg)

  return result

proc collectInfo*(domain: string): Future[InfoCollectionResult] {.async.} =
  ## 主信息收集函数
  logInfo("Starting comprehensive information collection for: " & domain)

  var result = InfoCollectionResult(domain: domain)

  # 1. 证书信息收集
  logDebug("Collecting certificate information...")
  result.certInfo = await getCertificateInfo(domain)

  await sleepAsync(1000)

  # 2. WHOIS信息收集
  logDebug("Collecting WHOIS information...")
  result.whoisInfo = await getWhoisInfo(domain)

  await sleepAsync(1000)

  # 3. ICP备案检查
  logDebug("Checking ICP registration...")
  result.icpInfo = await checkICPRecord(domain)

  await sleepAsync(2000)

  # 4. 搜索引擎查询
  logDebug("Performing search engine queries...")
  let searchEngines = @["google", "bing", "baidu"]
  for engine in searchEngines:
    let searchResult = await searchEngineQuery(domain, engine)
    result.searchResults.add(searchResult)
    await sleepAsync(3000)  # 搜索引擎频率限制

  # 5. GitHub泄露检查
  logDebug("Checking GitHub for potential leaks...")
  result.githubLeaks = await checkGitHubLeaks(domain)

  logSuccess("Information collection completed for: " & domain)
  return result
