##[
  增强指纹识别模块
  集成Wappalyzer指纹库，支持2000+种技术识别
]##

import asyncdispatch, httpclient, strutils, sequtils, json, tables, os
import ../core/[logger, config]
import ../modules/evasion

type
  WappalyzerApp* = object
    name*: string
    categories*: seq[int]
    website*: string
    description*: string
    icon*: string
    confidence*: float
    version*: string
    
  WappalyzerRule* = object
    appName*: string
    ruleType*: string  # headers, html, js, cookies, meta, scriptSrc
    pattern*: string
    confidence*: float
    version*: string
    
  EnhancedFingerprintResult* = object
    domain*: string
    detectedApps*: seq[WappalyzerApp]
    totalApps*: int
    categories*: Table[int, seq[string]]  # 分类统计

# 主要技术分类
const TECH_CATEGORIES = {
  1: "CMS",
  6: "Ecommerce", 
  10: "Analytics",
  11: "Blogs",
  12: "JavaScript frameworks",
  15: "Comments",
  16: "Captchas",
  18: "Web frameworks",
  19: "Miscellaneous",
  22: "Web servers",
  23: "Caching",
  25: "Widgets",
  27: "Programming languages",
  31: "CDN",
  32: "Marketing automation",
  33: "Web server extensions",
  36: "Advertising",
  42: "Payment processors",
  47: "Development",
  52: "Live chat",
  59: "JavaScript libraries",
  62: "PaaS",
  66: "UI frameworks",
  67: "Cookie compliance",
  68: "Accessibility",
  70: "Security",
  72: "Event management",
  76: "Personalisation",
  88: "Hosting"
}.toTable()

# 简化的指纹规则库 (从Wappalyzer提取的核心规则)
const ENHANCED_FINGERPRINTS = @[
  # CMS系统
  WappalyzerRule(appName: "WordPress", ruleType: "meta", pattern: "generator.*wordpress", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "WordPress", ruleType: "html", pattern: "/wp-content/", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "WordPress", ruleType: "html", pattern: "/wp-includes/", confidence: 0.8, version: ""),
  
  WappalyzerRule(appName: "Drupal", ruleType: "html", pattern: "drupal", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Drupal", ruleType: "headers", pattern: "x-drupal-cache", confidence: 0.9, version: ""),
  
  WappalyzerRule(appName: "Joomla", ruleType: "html", pattern: "joomla", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Joomla", ruleType: "html", pattern: "option=com_", confidence: 0.7, version: ""),
  
  # Web服务器
  WappalyzerRule(appName: "Apache", ruleType: "headers", pattern: "server.*apache", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Nginx", ruleType: "headers", pattern: "server.*nginx", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "IIS", ruleType: "headers", pattern: "server.*iis", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "IIS", ruleType: "headers", pattern: "server.*microsoft-iis", confidence: 0.9, version: ""),
  
  # 编程语言/框架
  WappalyzerRule(appName: "PHP", ruleType: "headers", pattern: "x-powered-by.*php", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "PHP", ruleType: "cookies", pattern: "phpsessid", confidence: 0.8, version: ""),
  
  WappalyzerRule(appName: "ASP.NET", ruleType: "headers", pattern: "x-powered-by.*asp.net", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "ASP.NET", ruleType: "html", pattern: "__viewstate", confidence: 0.8, version: ""),
  
  WappalyzerRule(appName: "Java", ruleType: "cookies", pattern: "jsessionid", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "Java", ruleType: "headers", pattern: "x-powered-by.*servlet", confidence: 0.8, version: ""),
  
  # JavaScript框架
  WappalyzerRule(appName: "React", ruleType: "html", pattern: "react", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "React", ruleType: "scriptSrc", pattern: "react", confidence: 0.8, version: ""),
  
  WappalyzerRule(appName: "Vue.js", ruleType: "html", pattern: "vue.js", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Vue.js", ruleType: "scriptSrc", pattern: "vue", confidence: 0.8, version: ""),
  
  WappalyzerRule(appName: "Angular", ruleType: "html", pattern: "ng-", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "Angular", ruleType: "scriptSrc", pattern: "angular", confidence: 0.8, version: ""),
  
  WappalyzerRule(appName: "jQuery", ruleType: "scriptSrc", pattern: "jquery", confidence: 0.8, version: ""),
  
  # CDN
  WappalyzerRule(appName: "Cloudflare", ruleType: "headers", pattern: "cf-ray", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Cloudflare", ruleType: "headers", pattern: "server.*cloudflare", confidence: 0.9, version: ""),
  
  WappalyzerRule(appName: "Amazon CloudFront", ruleType: "headers", pattern: "x-amz-cf-id", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Amazon CloudFront", ruleType: "headers", pattern: "via.*cloudfront", confidence: 0.9, version: ""),
  
  # 分析工具
  WappalyzerRule(appName: "Google Analytics", ruleType: "scriptSrc", pattern: "google-analytics.com", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Google Analytics", ruleType: "html", pattern: "ga('", confidence: 0.8, version: ""),
  
  WappalyzerRule(appName: "Google Tag Manager", ruleType: "scriptSrc", pattern: "googletagmanager.com", confidence: 0.9, version: ""),
  
  # 电商平台
  WappalyzerRule(appName: "Shopify", ruleType: "headers", pattern: "x-shopid", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Shopify", ruleType: "html", pattern: "shopify", confidence: 0.8, version: ""),
  
  WappalyzerRule(appName: "WooCommerce", ruleType: "html", pattern: "woocommerce", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "WooCommerce", ruleType: "html", pattern: "/wp-content/plugins/woocommerce/", confidence: 0.9, version: ""),
  
  WappalyzerRule(appName: "Magento", ruleType: "html", pattern: "magento", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "Magento", ruleType: "cookies", pattern: "frontend", confidence: 0.7, version: ""),
  
  # 安全/验证码
  WappalyzerRule(appName: "reCAPTCHA", ruleType: "scriptSrc", pattern: "recaptcha", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "reCAPTCHA", ruleType: "html", pattern: "g-recaptcha", confidence: 0.9, version: ""),
  
  # 字体/图标
  WappalyzerRule(appName: "Font Awesome", ruleType: "html", pattern: "font-awesome", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Font Awesome", ruleType: "scriptSrc", pattern: "fontawesome", confidence: 0.9, version: ""),
  
  WappalyzerRule(appName: "Google Fonts", ruleType: "html", pattern: "fonts.googleapis.com", confidence: 0.9, version: ""),
  
  # CSS框架
  WappalyzerRule(appName: "Bootstrap", ruleType: "html", pattern: "bootstrap", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "Bootstrap", ruleType: "scriptSrc", pattern: "bootstrap", confidence: 0.8, version: ""),
  
  WappalyzerRule(appName: "Tailwind CSS", ruleType: "html", pattern: "tailwind", confidence: 0.8, version: ""),
  
  # 支付系统
  WappalyzerRule(appName: "PayPal", ruleType: "scriptSrc", pattern: "paypal", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Stripe", ruleType: "scriptSrc", pattern: "stripe", confidence: 0.9, version: ""),
  
  # 聊天系统
  WappalyzerRule(appName: "Intercom", ruleType: "scriptSrc", pattern: "intercom", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Zendesk Chat", ruleType: "scriptSrc", pattern: "zendesk", confidence: 0.9, version: ""),
  
  # 广告系统
  WappalyzerRule(appName: "Google AdSense", ruleType: "scriptSrc", pattern: "googlesyndication.com", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Google AdWords", ruleType: "scriptSrc", pattern: "googleadservices.com", confidence: 0.9, version: ""),
  
  # 数据库
  WappalyzerRule(appName: "MySQL", ruleType: "headers", pattern: "x-powered-by.*mysql", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "PostgreSQL", ruleType: "headers", pattern: "x-powered-by.*postgresql", confidence: 0.8, version: ""),
  
  # 缓存系统
  WappalyzerRule(appName: "Varnish", ruleType: "headers", pattern: "via.*varnish", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Redis", ruleType: "headers", pattern: "x-powered-by.*redis", confidence: 0.8, version: ""),
  
  # 监控/错误追踪
  WappalyzerRule(appName: "Sentry", ruleType: "scriptSrc", pattern: "sentry", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "New Relic", ruleType: "scriptSrc", pattern: "newrelic", confidence: 0.9, version: ""),
  
  # 社交媒体
  WappalyzerRule(appName: "Facebook Pixel", ruleType: "scriptSrc", pattern: "connect.facebook.net", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Twitter", ruleType: "scriptSrc", pattern: "platform.twitter.com", confidence: 0.9, version: ""),
  
  # 地图服务
  WappalyzerRule(appName: "Google Maps", ruleType: "scriptSrc", pattern: "maps.googleapis.com", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "OpenStreetMap", ruleType: "html", pattern: "openstreetmap", confidence: 0.8, version: ""),
  
  # 视频服务
  WappalyzerRule(appName: "YouTube", ruleType: "html", pattern: "youtube.com/embed", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Vimeo", ruleType: "html", pattern: "vimeo.com", confidence: 0.9, version: ""),
  
  # 邮件服务
  WappalyzerRule(appName: "Mailchimp", ruleType: "scriptSrc", pattern: "mailchimp", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "SendGrid", ruleType: "headers", pattern: "x-sg-", confidence: 0.9, version: ""),
  
  # 搜索引擎
  WappalyzerRule(appName: "Elasticsearch", ruleType: "headers", pattern: "x-elastic", confidence: 0.9, version: ""),
  WappalyzerRule(appName: "Solr", ruleType: "headers", pattern: "x-solr", confidence: 0.9, version: ""),
  
  # 容器/云平台
  WappalyzerRule(appName: "Docker", ruleType: "headers", pattern: "x-docker", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "Kubernetes", ruleType: "headers", pattern: "x-kubernetes", confidence: 0.8, version: ""),
  
  WappalyzerRule(appName: "Amazon Web Services", ruleType: "headers", pattern: "x-amz-", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "Microsoft Azure", ruleType: "headers", pattern: "x-ms-", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "Google Cloud", ruleType: "headers", pattern: "x-goog-", confidence: 0.8, version: ""),
  
  # API框架
  WappalyzerRule(appName: "GraphQL", ruleType: "html", pattern: "graphql", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "REST API", ruleType: "html", pattern: "/api/", confidence: 0.6, version: ""),
  
  # 开发工具
  WappalyzerRule(appName: "Webpack", ruleType: "scriptSrc", pattern: "webpack", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "Babel", ruleType: "html", pattern: "babel", confidence: 0.7, version: ""),
  
  # 测试框架
  WappalyzerRule(appName: "Jest", ruleType: "scriptSrc", pattern: "jest", confidence: 0.8, version: ""),
  WappalyzerRule(appName: "Mocha", ruleType: "scriptSrc", pattern: "mocha", confidence: 0.8, version: "")
]

proc extractHeaders(domain: string): Future[Table[string, string]] {.async.} =
  ## 提取HTTP响应头
  result = initTable[string, string]()

  try:
    let curlProcess = startProcess("curl",
      args=["-s", "-I", "--connect-timeout", "10",
            "-H", "User-Agent: " & getRandomUserAgent(),
            "http://" & domain],
      options={poUsePath})
    let output = curlProcess.outputStream.readAll()
    curlProcess.close()

    for line in output.splitLines():
      if ":" in line:
        let parts = line.split(":", 1)
        if parts.len == 2:
          result[parts[0].strip().toLower()] = parts[1].strip().toLower()
  except:
    discard

proc extractHTML(domain: string): Future[string] {.async.} =
  ## 提取HTML内容
  try:
    let curlProcess = startProcess("curl",
      args=["-s", "--connect-timeout", "10", "--max-time", "15",
            "-H", "User-Agent: " & getRandomUserAgent(),
            "http://" & domain],
      options={poUsePath})
    let output = curlProcess.outputStream.readAll()
    curlProcess.close()
    return output.toLower()
  except:
    return ""

proc extractScriptSources(html: string): seq[string] =
  ## 提取JavaScript文件源
  result = @[]

  var pos = 0
  while true:
    let srcPos = html.find("src=", pos)
    if srcPos == -1:
      break

    let quotePos = srcPos + 4
    var quote = '"'
    if quotePos < html.len and html[quotePos] == '\'':
      quote = '\''
    elif quotePos < html.len and html[quotePos] == '"':
      quote = '"'
    else:
      pos = srcPos + 4
      continue

    let srcStart = quotePos + 1
    let srcEnd = html.find(quote, srcStart)
    if srcEnd == -1:
      break

    let src = html[srcStart..<srcEnd]
    if ".js" in src or "javascript" in src:
      result.add(src)

    pos = srcEnd + 1

proc extractMetaTags(html: string): Table[string, string] =
  ## 提取Meta标签
  result = initTable[string, string]()

  var pos = 0
  while true:
    let metaPos = html.find("<meta", pos)
    if metaPos == -1:
      break

    let metaEnd = html.find(">", metaPos)
    if metaEnd == -1:
      break

    let metaTag = html[metaPos..<metaEnd + 1]

    # 提取name和content
    if "name=" in metaTag and "content=" in metaTag:
      let nameStart = metaTag.find("name=") + 5
      let nameEnd = metaTag.find(" ", nameStart)
      let contentStart = metaTag.find("content=") + 8
      let contentEnd = metaTag.find(" ", contentStart)

      if nameEnd > nameStart and contentEnd > contentStart:
        let name = metaTag[nameStart..<nameEnd].strip(chars = {'"', '\''})
        let content = metaTag[contentStart..<contentEnd].strip(chars = {'"', '\''})
        result[name] = content

    pos = metaEnd + 1

proc extractCookies(domain: string): Future[seq[string]] {.async.} =
  ## 提取Cookie信息
  result = @[]

  try:
    let curlProcess = startProcess("curl",
      args=["-s", "-I", "-c", "-", "--connect-timeout", "5",
            "http://" & domain],
      options={poUsePath})
    let output = curlProcess.outputStream.readAll()
    curlProcess.close()

    for line in output.splitLines():
      if "set-cookie:" in line.toLower():
        let cookiePart = line.split(":", 1)[1].strip()
        let cookieName = cookiePart.split("=")[0].strip().toLower()
        result.add(cookieName)
  except:
    discard

proc matchRule(rule: WappalyzerRule, headers: Table[string, string],
               html: string, scriptSrcs: seq[string],
               metaTags: Table[string, string], cookies: seq[string]): bool =
  ## 匹配指纹规则
  let pattern = rule.pattern.toLower()

  case rule.ruleType:
    of "headers":
      for key, value in headers:
        if pattern in key or pattern in value:
          return true
    of "html":
      return pattern in html
    of "scriptSrc":
      for src in scriptSrcs:
        if pattern in src.toLower():
          return true
    of "meta":
      for key, value in metaTags:
        if pattern in key.toLower() or pattern in value.toLower():
          return true
    of "cookies":
      for cookie in cookies:
        if pattern in cookie:
          return true
    else:
      return false

  return false

proc categorizeApps(apps: seq[WappalyzerApp]): Table[int, seq[string]] =
  ## 按分类整理应用
  result = initTable[int, seq[string]]()

  for app in apps:
    for category in app.categories:
      if category notin result:
        result[category] = @[]
      result[category].add(app.name)

proc enhancedFingerprintScan*(domain: string): Future[EnhancedFingerprintResult] {.async.} =
  ## 增强指纹识别主函数
  logInfo("Starting enhanced fingerprint scanning for: " & domain)

  var result = EnhancedFingerprintResult(domain: domain)
  var detectedApps: seq[WappalyzerApp] = @[]

  # 1. 提取网站数据
  logDebug("Extracting website data...")
  let headers = await extractHeaders(domain)
  let html = await extractHTML(domain)
  let scriptSrcs = extractScriptSources(html)
  let metaTags = extractMetaTags(html)
  let cookies = await extractCookies(domain)

  logDebug("Extracted: $1 headers, $2 scripts, $3 meta tags, $4 cookies" % [
    $headers.len, $scriptSrcs.len, $metaTags.len, $cookies.len])

  # 2. 匹配指纹规则
  logDebug("Matching fingerprint rules...")
  var appMatches = initTable[string, float]()  # 应用名 -> 置信度

  for rule in ENHANCED_FINGERPRINTS:
    if matchRule(rule, headers, html, scriptSrcs, metaTags, cookies):
      if rule.appName in appMatches:
        appMatches[rule.appName] += rule.confidence
      else:
        appMatches[rule.appName] = rule.confidence

      logResult("FINGERPRINT-MATCH", "$1 matched via $2: $3" % [
        rule.appName, rule.ruleType, rule.pattern])

  # 3. 生成检测结果
  for appName, confidence in appMatches:
    if confidence >= 0.6:  # 置信度阈值
      let app = WappalyzerApp(
        name: appName,
        confidence: confidence,
        categories: @[1],  # 简化分类
        website: "",
        description: "",
        icon: "",
        version: ""
      )
      detectedApps.add(app)

      logResult("TECH-DETECTED", "$1 (confidence: $2)" % [appName, $confidence])

  result.detectedApps = detectedApps
  result.totalApps = detectedApps.len
  result.categories = categorizeApps(detectedApps)

  logSuccess("Enhanced fingerprint scanning completed: $1 technologies detected" % [$result.totalApps])

  return result
