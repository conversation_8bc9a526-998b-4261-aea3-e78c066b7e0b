##[
  DNS历史记录查询模块
  通过多个免费API查询DNS历史解析记录
]##

import asyncdispatch, httpclient, json, strutils, sequtils, times, net, nativesockets, osproc, streams
import ../core/[logger, config]
import ../modules/evasion

type
  DNSRecord* = object
    ip*: string
    firstSeen*: string
    lastSeen*: string
    recordType*: string
    source*: string

  DNSHistoryResult* = object
    domain*: string
    records*: seq[DNSRecord]
    oldestIP*: string
    isRealIP*: bool

# 免费DNS历史查询API列表
const DNS_HISTORY_APIS = @[
  "hackertarget",
  "crtsh",
  "securitytrails",
  "dnsdb",
  "viewdns"
]

proc isValidIP(ip: string): bool =
  ## 验证IP地址格式
  try:
    discard net.parseIpAddress(ip)
    return true
  except:
    return false

proc queryHackerTarget(domain: string): Future[seq[DNSRecord]] {.async.} =
  ## 查询HackerTarget API (使用更稳定的方法)
  result = @[]

  try:
    # 使用curl命令避免SSL问题
    let curlProcess = startProcess("curl",
      args=["-s", "--connect-timeout", "10",
            "http://api.hackertarget.com/hostsearch/?q=" & domain],
      options={poUsePath})
    let output = curlProcess.outputStream.readAll()
    let exitCode = curlProcess.waitForExit()
    curlProcess.close()

    if exitCode == 0:
      for line in output.splitLines():
        if line.strip() != "" and "," in line:
          let parts = line.split(",")
          if parts.len >= 2:
            let subdomain = parts[0].strip()
            let ip = parts[1].strip()

            # 验证IP格式
            if isValidIP(ip):
              result.add(DNSRecord(
                ip: ip,
                firstSeen: "unknown",
                lastSeen: "unknown",
                recordType: "A",
                source: "hackertarget"
              ))

      logResult("DNS-HISTORY", "HackerTarget found $1 valid records" % [$result.len])
    else:
      logWarn("HackerTarget query failed via curl")

  except Exception as e:
    logWarn("HackerTarget query failed: " & e.msg)
    result = @[]

proc queryCrtSh(domain: string): Future[seq[DNSRecord]] {.async.} =
  ## 查询crt.sh证书透明度日志 (使用curl避免连接问题)
  result = @[]

  try:
    # 使用curl命令，更稳定
    let url = "https://crt.sh/?q=%25." & domain & "&output=json"
    let curlProcess = startProcess("curl",
      args=["-s", "--connect-timeout", "15", "--max-time", "30",
            "-H", "User-Agent: Mozilla/5.0 (compatible; KaiTian/1.0)",
            url],
      options={poUsePath})
    let output = curlProcess.outputStream.readAll()
    let exitCode = curlProcess.waitForExit()
    curlProcess.close()

    if exitCode == 0 and output.len > 10:
      try:
        let jsonData = parseJson(output)

        for cert in jsonData:
          if cert.hasKey("name_value"):
            let names = cert["name_value"].getStr().split("\n")
            for name in names:
              let cleanName = name.strip()
              if cleanName != domain and cleanName.endsWith("." & domain):
                # 过滤通配符域名，只处理具体的子域名
                if not cleanName.startsWith("*") and cleanName != domain:
                  result.add(DNSRecord(
                    ip: cleanName,  # 存储子域名，后续可以解析IP
                    firstSeen: cert{"not_before"}.getStr("unknown"),
                    lastSeen: cert{"not_after"}.getStr("unknown"),
                    recordType: "SUBDOMAIN",
                    source: "crtsh"
                  ))

        logResult("DNS-HISTORY", "crt.sh found $1 subdomains" % [$result.len])
      except:
        logWarn("crt.sh returned invalid JSON data")
    else:
      logWarn("crt.sh query failed or returned empty data")

  except Exception as e:
    logWarn("crt.sh query failed: " & e.msg)
    result = @[]

proc queryAlternativeDNS(domain: string): Future[seq[DNSRecord]] {.async.} =
  ## 使用替代方法查询DNS信息
  result = @[]

  try:
    # 方法1: 使用dig命令查询历史记录
    let digProcess = startProcess("dig", args=["+short", domain], options={poUsePath})
    let digOutput = digProcess.outputStream.readAll()
    digProcess.close()

    for line in digOutput.splitLines():
      let ip = line.strip()
      if ip.len > 0 and isValidIP(ip):
        result.add(DNSRecord(
          ip: ip,
          firstSeen: "unknown",
          lastSeen: "recent",
          recordType: "A",
          source: "dig"
        ))

    logResult("DNS-HISTORY", "Alternative DNS found $1 records" % [$result.len])

  except Exception as e:
    logWarn("Alternative DNS query failed: " & e.msg)
    result = @[]

proc querySecurityTrails(domain: string): Future[seq[DNSRecord]] {.async.} =
  ## 查询SecurityTrails (跳过，避免403错误)
  logWarn("SecurityTrails query skipped (requires API key)")
  return @[]



proc isCloudflareIP(ip: string): bool =
  ## 检查IP是否属于Cloudflare
  let cfRanges = @[
    "103.21.244.", "103.22.200.", "103.31.4.", "104.16.", "104.17.",
    "104.18.", "104.19.", "104.20.", "104.21.", "104.22.", "104.23.",
    "104.24.", "104.25.", "104.26.", "104.27.", "104.28.", "108.162.192.",
    "131.0.72.", "141.101.64.", "162.158.", "172.64.", "172.65.",
    "172.66.", "172.67.", "173.245.48.", "188.114.96.", "190.93.240.",
    "197.234.240.", "198.41.128."
  ]

  for range in cfRanges:
    if ip.startsWith(range):
      return true
  return false

proc verifyRealIP(ip: string, domain: string): Future[bool] {.async.} =
  ## 验证IP是否为网站真实IP (增强版)

  # 首先检查是否为已知CDN IP
  if isCloudflareIP(ip):
    logWarn("IP $1 belongs to Cloudflare, not a real origin IP" % [ip])
    return false

  var client = newAsyncHttpClient()
  defer: client.close()

  try:
    client.headers = newHttpHeaders({
      "Host": domain,
      "User-Agent": "Mozilla/5.0 (compatible; KaiTian/1.0)"
    })

    # 直接访问IP，设置Host头
    let response = await client.get("http://" & ip)

    # 检查响应状态和内容
    if response.status.startsWith("200") or response.status.startsWith("30"):
      let body = await response.body
      let headers = response.headers

      # 检查是否有CDN特征头
      for key, value in headers:
        if "cloudflare" in key.toLower() or "cloudflare" in value.toLower():
          logWarn("Response contains Cloudflare headers, likely still behind CDN")
          return false
        if "cf-ray" in key.toLower():
          logWarn("Found CF-Ray header, still behind Cloudflare")
          return false

      # 内容验证
      if domain in body or "html" in body.toLower():
        logSuccess("Real IP verified: $1 -> $2 (bypassed CDN)" % [ip, domain])
        return true

    return false

  except Exception as e:
    logDebug("IP verification failed for $1: $2" % [ip, e.msg])
    return false

proc queryDNSHistory*(domain: string): Future[DNSHistoryResult] {.async.} =
  ## 主DNS历史查询函数 (改进错误处理)
  logInfo("Starting DNS history lookup for: " & domain)

  var allRecords: seq[DNSRecord] = @[]

  # 顺序查询API，避免并发导致的网络问题
  logDebug("Querying HackerTarget...")
  let hackerTargetRecords = await queryHackerTarget(domain)
  allRecords.add(hackerTargetRecords)

  await sleepAsync(2000)  # 2秒延迟

  logDebug("Querying crt.sh...")
  let crtShRecords = await queryCrtSh(domain)
  allRecords.add(crtShRecords)

  await sleepAsync(2000)  # 2秒延迟

  logDebug("Querying alternative DNS...")
  let altDNSRecords = await queryAlternativeDNS(domain)
  allRecords.add(altDNSRecords)

  # 去重
  var uniqueRecords: seq[DNSRecord] = @[]
  var seenIPs: seq[string] = @[]

  for record in allRecords:
    if record.ip notin seenIPs:
      seenIPs.add(record.ip)
      uniqueRecords.add(record)

  # 按时间排序找到最早的记录
  var oldestIP = ""
  var oldestTime = ""

  for record in uniqueRecords:
    if record.firstSeen != "unknown" and record.firstSeen != "":
      if oldestTime == "" or record.firstSeen < oldestTime:
        oldestTime = record.firstSeen
        oldestIP = record.ip

  # 如果没有时间信息，取第一个有效IP
  if oldestIP == "" and uniqueRecords.len > 0:
    for record in uniqueRecords:
      if isValidIP(record.ip):  # 确保是IP而不是域名
        oldestIP = record.ip
        break

  # 验证最早的IP是否为真实IP
  var isRealIP = false
  if oldestIP != "" and isValidIP(oldestIP):
    isRealIP = await verifyRealIP(oldestIP, domain)

  let result = DNSHistoryResult(
    domain: domain,
    records: uniqueRecords,
    oldestIP: oldestIP,
    isRealIP: isRealIP
  )

  logInfo("DNS history lookup completed: $1 records found" % [$uniqueRecords.len])
  if oldestIP != "":
    if isRealIP:
      logSuccess("Potential real IP found: " & oldestIP)
    else:
      logWarn("Oldest IP found but verification failed: " & oldestIP)

  return result

proc findRealIPBySubdomains(domain: string): Future[seq[string]] {.async.} =
  ## 通过子域名查找可能的真实IP
  let commonSubdomains = @["mail", "ftp", "admin", "test", "dev", "staging",
                          "origin", "direct", "www-origin", "backend"]

  result = @[]
  for subdomain in commonSubdomains:
    let fullDomain = subdomain & "." & domain
    try:
      let digProcess = startProcess("dig", args=["+short", fullDomain], options={poUsePath})
      let output = digProcess.outputStream.readAll()
      digProcess.close()

      for line in output.splitLines():
        let ip = line.strip()
        if ip.len > 0 and isValidIP(ip) and not isCloudflareIP(ip):
          if ip notin result:
            result.add(ip)
            logResult("REAL-IP-CANDIDATE", "Found non-CDN IP via $1: $2" % [fullDomain, ip])
    except:
      continue

proc findRealIP*(domain: string): Future[string] {.async.} =
  ## 综合查找真实IP (增强版)
  logInfo("Starting comprehensive real IP discovery for: " & domain)

  # 方法1: DNS历史记录
  let historyResult = await queryDNSHistory(domain)

  if historyResult.isRealIP and historyResult.oldestIP != "":
    return historyResult.oldestIP

  # 方法2: 验证历史记录中的其他IP
  for record in historyResult.records:
    if isValidIP(record.ip):  # 确保是IP而不是域名
      let isReal = await verifyRealIP(record.ip, domain)
      if isReal:
        logSuccess("Real IP found through verification: " & record.ip)
        return record.ip

  # 方法3: 通过子域名查找
  let subdomainIPs = await findRealIPBySubdomains(domain)
  for ip in subdomainIPs:
    let isReal = await verifyRealIP(ip, domain)
    if isReal:
      logSuccess("Real IP found via subdomain: " & ip)
      return ip

  logWarn("No real IP found, target may be fully protected by CDN")
  return ""
