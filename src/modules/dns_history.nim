##[
  DNS历史记录查询模块
  通过多个免费API查询DNS历史解析记录
]##

import asyncdispatch, httpclient, json, strutils, sequtils, times, net, nativesockets, osproc, streams
import ../core/[logger, config]
import ../modules/evasion

type
  DNSRecord* = object
    ip*: string
    firstSeen*: string
    lastSeen*: string
    recordType*: string
    source*: string

  DNSHistoryResult* = object
    domain*: string
    records*: seq[DNSRecord]
    oldestIP*: string
    isRealIP*: bool

# 免费DNS历史查询API列表
const DNS_HISTORY_APIS = @[
  "hackertarget",
  "crtsh",
  "securitytrails",
  "dnsdb",
  "viewdns"
]

proc isValidIP(ip: string): bool =
  ## 验证IP地址格式
  try:
    discard net.parseIpAddress(ip)
    return true
  except:
    return false

proc queryHackerTarget(domain: string): Future[seq[DNSRecord]] {.async.} =
  ## 查询HackerTarget API (改用HTTP避免SSL问题)
  var client = newAsyncHttpClient()
  defer: client.close()

  try:
    await randomDelay()
    client.headers = getRandomHeaders()

    # 使用HTTP而不是HTTPS避免SSL问题
    let url = "http://api.hackertarget.com/hostsearch/?q=" & domain
    let response = await client.getContent(url)

    result = @[]
    for line in response.splitLines():
      if line.strip() != "" and "," in line:
        let parts = line.split(",")
        if parts.len >= 2:
          let subdomain = parts[0].strip()
          let ip = parts[1].strip()

          # 验证IP格式
          if isValidIP(ip):
            result.add(DNSRecord(
              ip: ip,
              firstSeen: "unknown",
              lastSeen: "unknown",
              recordType: "A",
              source: "hackertarget"
            ))

    logResult("DNS-HISTORY", "HackerTarget found $1 valid records" % [$result.len])

  except Exception as e:
    logWarn("HackerTarget query failed, trying alternative method: " & e.msg)
    result = @[]

proc queryCrtSh(domain: string): Future[seq[DNSRecord]] {.async.} =
  ## 查询crt.sh证书透明度日志
  var client = newAsyncHttpClient()
  defer: client.close()
  
  try:
    await randomDelay()
    client.headers = getRandomHeaders()
    
    let url = "https://crt.sh/?q=%25." & domain & "&output=json"
    let response = await client.getContent(url)
    let jsonData = parseJson(response)
    
    result = @[]
    var seenIPs: seq[string] = @[]
    
    for cert in jsonData:
      if cert.hasKey("name_value"):
        let names = cert["name_value"].getStr().split("\n")
        for name in names:
          let cleanName = name.strip()
          if cleanName != domain and cleanName.endsWith("." & domain):
            try:
              # 过滤通配符域名，只处理具体的子域名
              if not cleanName.startsWith("*") and cleanName != domain:
                result.add(DNSRecord(
                  ip: cleanName,  # 存储子域名，后续可以解析IP
                  firstSeen: cert{"not_before"}.getStr("unknown"),
                  lastSeen: cert{"not_after"}.getStr("unknown"),
                  recordType: "SUBDOMAIN",
                  source: "crtsh"
                ))
            except:
              continue
    
    logResult("DNS-HISTORY", "crt.sh found $1 unique IPs" % [$result.len])
    
  except Exception as e:
    logError("crt.sh query failed: " & e.msg)
    result = @[]

proc queryAlternativeDNS(domain: string): Future[seq[DNSRecord]] {.async.} =
  ## 使用替代方法查询DNS信息
  result = @[]

  try:
    # 方法1: 使用dig命令查询历史记录
    let digProcess = startProcess("dig", args=["+short", domain], options={poUsePath})
    let digOutput = digProcess.outputStream.readAll()
    digProcess.close()

    for line in digOutput.splitLines():
      let ip = line.strip()
      if ip.len > 0 and isValidIP(ip):
        result.add(DNSRecord(
          ip: ip,
          firstSeen: "unknown",
          lastSeen: "recent",
          recordType: "A",
          source: "dig"
        ))

    logResult("DNS-HISTORY", "Alternative DNS found $1 records" % [$result.len])

  except Exception as e:
    logWarn("Alternative DNS query failed: " & e.msg)
    result = @[]

proc querySecurityTrails(domain: string): Future[seq[DNSRecord]] {.async.} =
  ## 查询SecurityTrails (跳过，避免403错误)
  logWarn("SecurityTrails query skipped (requires API key)")
  return @[]



proc verifyRealIP(ip: string, domain: string): Future[bool] {.async.} =
  ## 验证IP是否为网站真实IP
  var client = newAsyncHttpClient()
  defer: client.close()
  
  try:
    client.headers = newHttpHeaders({
      "Host": domain,
      "User-Agent": "Mozilla/5.0 (compatible; KaiTian/1.0)"
    })
    
    # 直接访问IP，设置Host头
    let response = await client.get("http://" & ip)
    
    # 检查响应状态和内容
    if response.status.startsWith("200") or response.status.startsWith("30"):
      let body = await response.body
      
      # 简单的内容验证
      if domain in body or "html" in body.toLower():
        logSuccess("Real IP verified: $1 -> $2" % [ip, domain])
        return true
    
    return false
    
  except Exception as e:
    logDebug("IP verification failed for $1: $2" % [ip, e.msg])
    return false

proc queryDNSHistory*(domain: string): Future[DNSHistoryResult] {.async.} =
  ## 主DNS历史查询函数
  logInfo("Starting DNS history lookup for: " & domain)
  
  var allRecords: seq[DNSRecord] = @[]
  
  # 并发查询多个API
  let futures = @[
    queryHackerTarget(domain),
    queryCrtSh(domain),
    queryAlternativeDNS(domain)
  ]
  
  let results = await all(futures)
  
  # 合并所有结果
  for recordList in results:
    allRecords.add(recordList)
  
  # 去重
  var uniqueRecords: seq[DNSRecord] = @[]
  var seenIPs: seq[string] = @[]
  
  for record in allRecords:
    if record.ip notin seenIPs:
      seenIPs.add(record.ip)
      uniqueRecords.add(record)
  
  # 按时间排序找到最早的记录
  var oldestIP = ""
  var oldestTime = ""
  
  for record in uniqueRecords:
    if record.firstSeen != "unknown" and record.firstSeen != "":
      if oldestTime == "" or record.firstSeen < oldestTime:
        oldestTime = record.firstSeen
        oldestIP = record.ip
  
  # 如果没有时间信息，取第一个IP
  if oldestIP == "" and uniqueRecords.len > 0:
    oldestIP = uniqueRecords[0].ip
  
  # 验证最早的IP是否为真实IP
  var isRealIP = false
  if oldestIP != "":
    isRealIP = await verifyRealIP(oldestIP, domain)
  
  let result = DNSHistoryResult(
    domain: domain,
    records: uniqueRecords,
    oldestIP: oldestIP,
    isRealIP: isRealIP
  )
  
  logInfo("DNS history lookup completed: $1 records found" % [$uniqueRecords.len])
  if oldestIP != "":
    if isRealIP:
      logSuccess("Potential real IP found: " & oldestIP)
    else:
      logWarn("Oldest IP found but verification failed: " & oldestIP)
  
  return result

proc findRealIP*(domain: string): Future[string] {.async.} =
  ## 综合查找真实IP
  let historyResult = await queryDNSHistory(domain)
  
  if historyResult.isRealIP and historyResult.oldestIP != "":
    return historyResult.oldestIP
  
  # 如果历史记录验证失败，尝试验证其他IP
  for record in historyResult.records:
    let isReal = await verifyRealIP(record.ip, domain)
    if isReal:
      logSuccess("Real IP found through verification: " & record.ip)
      return record.ip
  
  return ""
