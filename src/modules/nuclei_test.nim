##[
  测试版Nuclei风格漏洞扫描
]##

import asyncdispatch, httpclient, strutils, sequtils, json, tables, os, osproc, streams
import ../core/[logger, config]

type
  TestVulnResult* = object
    name*: string
    severity*: string
    matched*: bool
    url*: string

proc testNucleiScan*(domain: string): Future[seq[TestVulnResult]] {.async.} =
  ## 测试Nuclei风格扫描
  result = @[]
  
  logInfo("Starting test Nuclei scan for: " & domain)
  
  # 测试1: Log4j检测
  try:
    let url1 = "http://" & domain & "/?x=test"
    let curlProcess1 = startProcess("curl", args=["-s", "--connect-timeout", "5", url1], options={poUsePath})
    let response1 = curlProcess1.outputStream.readAll()
    curlProcess1.close()
    
    let result1 = TestVulnResult(
      name: "Log4j Test",
      severity: "critical",
      matched: "error" in response1.toLower(),
      url: url1
    )
    result.add(result1)
    
    if result1.matched:
      logResult("VULN-TEST", "Log4j test matched")
  except:
    discard
  
  # 测试2: Admin面板检测
  try:
    let url2 = "http://" & domain & "/admin"
    let curlProcess2 = startProcess("curl", args=["-s", "-I", "--connect-timeout", "5", url2], options={poUsePath})
    let response2 = curlProcess2.outputStream.readAll()
    curlProcess2.close()
    
    let result2 = TestVulnResult(
      name: "Admin Panel Test",
      severity: "info",
      matched: "200 OK" in response2,
      url: url2
    )
    result.add(result2)
    
    if result2.matched:
      logResult("VULN-TEST", "Admin panel found")
  except:
    discard
  
  # 测试3: 敏感文件检测
  try:
    let url3 = "http://" & domain & "/.env"
    let curlProcess3 = startProcess("curl", args=["-s", "--connect-timeout", "5", url3], options={poUsePath})
    let response3 = curlProcess3.outputStream.readAll()
    curlProcess3.close()
    
    let result3 = TestVulnResult(
      name: "Sensitive File Test",
      severity: "high",
      matched: ("password" in response3.toLower() or "api_key" in response3.toLower()),
      url: url3
    )
    result.add(result3)
    
    if result3.matched:
      logResult("VULN-TEST", "Sensitive file found")
  except:
    discard
  
  logSuccess("Test Nuclei scan completed: $1 tests run" % [$result.len])
  return result
