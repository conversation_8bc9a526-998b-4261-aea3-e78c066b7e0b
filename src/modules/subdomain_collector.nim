##[
  子域名收集模块
  实现被动收集(证书透明度)和主动收集(字典爆破)，包括IP解析
]##

import asyncdispatch, httpclient, json, strutils, sequtils, osproc, streams, os
import ../core/[logger, config]
import ../modules/evasion

type
  SubdomainInfo* = object
    subdomain*: string
    ip*: string
    isAlive*: bool
    httpStatus*: int
    title*: string

  SubdomainResult* = object
    domain*: string
    subdomains*: seq[SubdomainInfo]
    totalFound*: int
    aliveCount*: int

proc resolveSubdomainIP(subdomain: string): Future[string] {.async.} =
  ## 解析子域名IP
  try:
    let digProcess = startProcess("dig", args=["+short", subdomain], options={poUsePath})
    let output = digProcess.outputStream.readAll()
    digProcess.close()

    for line in output.splitLines():
      let ip = line.strip()
      if ip.len > 0 and not ip.contains("NXDOMAIN") and "." in ip:
        # 简单验证IP格式
        let parts = ip.split(".")
        if parts.len == 4:
          return ip

    return ""
  except:
    return ""

proc checkSubdomainAlive(subdomain: string): Future[tuple[alive: bool, status: int, title: string]] {.async.} =
  ## 检查子域名是否存活
  try:
    let curlProcess = startProcess("curl",
      args=["-s", "-I", "--connect-timeout", "5", "--max-time", "10",
            "-H", "User-Agent: " & getRandomUserAgent(),
            "http://" & subdomain],
      options={poUsePath})
    let output = curlProcess.outputStream.readAll()
    let exitCode = curlProcess.waitForExit()
    curlProcess.close()

    if exitCode == 0:
      let lines = output.splitLines()
      for line in lines:
        if line.startsWith("HTTP/"):
          let parts = line.split(" ")
          if parts.len >= 2:
            try:
              let status = parseInt(parts[1])
              if status >= 200 and status < 500:
                # 获取页面标题
                let titleProcess = startProcess("curl",
                  args=["-s", "--connect-timeout", "3", "--max-time", "5",
                        "-H", "User-Agent: " & getRandomUserAgent(),
                        "http://" & subdomain],
                  options={poUsePath})
                let htmlOutput = titleProcess.outputStream.readAll()
                titleProcess.close()

                var title = ""
                if "<title>" in htmlOutput:
                  let titleStart = htmlOutput.find("<title>") + 7
                  let titleEnd = htmlOutput.find("</title>", titleStart)
                  if titleEnd > titleStart:
                    title = htmlOutput[titleStart..<titleEnd].strip()

                return (true, status, title)
            except:
              continue

    return (false, 0, "")
  except:
    return (false, 0, "")

proc passiveSubdomainCollection(domain: string): Future[seq[string]] {.async.} =
  ## 被动子域名收集
  result = @[]

  logDebug("Starting passive subdomain collection...")

  # 1. 证书透明度日志
  try:
    let crtUrl = "https://crt.sh/?q=%25." & domain & "&output=json"
    let curlProcess = startProcess("curl",
      args=["-s", "--connect-timeout", "15", "--max-time", "30",
            "-H", "User-Agent: " & getRandomUserAgent(),
            crtUrl],
      options={poUsePath})
    let output = curlProcess.outputStream.readAll()
    let exitCode = curlProcess.waitForExit()
    curlProcess.close()

    if exitCode == 0 and output.len > 10:
      try:
        let jsonData = parseJson(output)
        for cert in jsonData:
          if cert.hasKey("name_value"):
            let names = cert["name_value"].getStr().split("\n")
            for name in names:
              let cleanName = name.strip()
              if cleanName != domain and cleanName.endsWith("." & domain):
                if not cleanName.startsWith("*") and cleanName notin result:
                  result.add(cleanName)
      except:
        logWarn("Failed to parse crt.sh JSON response")

    logResult("PASSIVE-CERT", "Found $1 subdomains from certificate logs" % [$result.len])
  except Exception as e:
    logWarn("Certificate transparency query failed: " & e.msg)

  # 2. DNS数据库查询 (使用HackerTarget)
  try:
    await sleepAsync(2000)
    let hackertargetUrl = "http://api.hackertarget.com/hostsearch/?q=" & domain
    let curlProcess = startProcess("curl",
      args=["-s", "--connect-timeout", "10",
            hackertargetUrl],
      options={poUsePath})
    let output = curlProcess.outputStream.readAll()
    curlProcess.close()

    for line in output.splitLines():
      if "," in line:
        let parts = line.split(",")
        if parts.len >= 2:
          let subdomain = parts[0].strip()
          if subdomain != domain and subdomain.endsWith("." & domain):
            if subdomain notin result:
              result.add(subdomain)

    logResult("PASSIVE-DNS", "Total unique subdomains from passive collection: $1" % [$result.len])
  except Exception as e:
    logWarn("DNS database query failed: " & e.msg)

  return result

proc activeSubdomainCollection(domain: string): Future[seq[string]] {.async.} =
  ## 主动子域名收集 (字典爆破)
  result = @[]

  logDebug("Starting active subdomain collection...")

  # 读取字典文件
  let wordlistPath = "./wordlists/subdomains.txt"
  if not fileExists(wordlistPath):
    logWarn("Subdomain wordlist not found: " & wordlistPath)
    return result

  let wordlist = readFile(wordlistPath).splitLines()
  logInfo("Loaded $1 subdomain patterns for brute force" % [$wordlist.len])

  var checkedCount = 0
  let totalCount = wordlist.len

  for word in wordlist:
    let cleanWord = word.strip()
    if cleanWord.len > 0 and not cleanWord.startsWith("#"):
      let subdomain = cleanWord & "." & domain

      # 检查DNS解析
      let ip = await resolveSubdomainIP(subdomain)
      if ip != "":
        result.add(subdomain)
        logResult("ACTIVE-FOUND", "$1 -> $2" % [subdomain, ip])

      checkedCount += 1
      if checkedCount mod 50 == 0:
        logDebug("Brute force progress: $1/$2 ($3%)" % [
          $checkedCount, $totalCount, $(checkedCount * 100 div totalCount)])

      # 小延迟避免DNS服务器限制
      await sleepAsync(100)

  logResult("ACTIVE-BRUTE", "Found $1 subdomains via brute force" % [$result.len])
  return result

proc collectSubdomains*(domain: string): Future[SubdomainResult] {.async.} =
  ## 主子域名收集函数
  logInfo("Starting comprehensive subdomain collection for: " & domain)

  var result = SubdomainResult(domain: domain)
  var allSubdomains: seq[string] = @[]

  # 1. 被动收集
  let passiveSubdomains = await passiveSubdomainCollection(domain)
  allSubdomains.add(passiveSubdomains)

  # 2. 主动收集
  let activeSubdomains = await activeSubdomainCollection(domain)
  allSubdomains.add(activeSubdomains)

  # 去重
  var uniqueSubdomains: seq[string] = @[]
  for subdomain in allSubdomains:
    if subdomain notin uniqueSubdomains:
      uniqueSubdomains.add(subdomain)

  result.totalFound = uniqueSubdomains.len
  logInfo("Total unique subdomains found: $1" % [$result.totalFound])

  # 3. 解析IP和存活检测
  logDebug("Resolving IPs and checking subdomain status...")
  var aliveCount = 0

  for subdomain in uniqueSubdomains:
    var subInfo = SubdomainInfo(subdomain: subdomain)

    # 解析IP
    subInfo.ip = await resolveSubdomainIP(subdomain)

    # 检查存活状态
    if subInfo.ip != "":
      let (alive, status, title) = await checkSubdomainAlive(subdomain)
      subInfo.isAlive = alive
      subInfo.httpStatus = status
      subInfo.title = title

      if alive:
        aliveCount += 1
        logResult("SUBDOMAIN-ALIVE", "$1 [$2] $3 - $4" % [
          subdomain, $status, subInfo.ip, title])
      else:
        logResult("SUBDOMAIN-FOUND", "$1 -> $2" % [subdomain, subInfo.ip])

    result.subdomains.add(subInfo)

    # 小延迟
    await sleepAsync(200)

  result.aliveCount = aliveCount

  logSuccess("Subdomain collection completed: $1 total, $2 alive" % [
    $result.totalFound, $result.aliveCount])

  return result
