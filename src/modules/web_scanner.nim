##[
  Web探测模块
  实现爬虫、JS文件扫描、API模糊测试、敏感信息提取
]##

import asyncdispatch, httpclient, strutils, sequtils, re, osproc, streams
import ../core/[logger, config]
import ../modules/evasion

type
  WebPage* = object
    url*: string
    title*: string
    statusCode*: int
    contentLength*: int
    headers*: seq[tuple[key: string, value: string]]
    links*: seq[string]
    forms*: seq[string]
    jsFiles*: seq[string]

  SensitiveInfo* = object
    infoType*: string
    content*: string
    location*: string

  APIEndpoint* = object
    url*: string
    httpMethod*: string
    parameters*: seq[string]

  WebScanResult* = object
    domain*: string
    pages*: seq[WebPage]
    jsFiles*: seq[string]
    apiEndpoints*: seq[APIEndpoint]
    sensitiveInfo*: seq[SensitiveInfo]
    directories*: seq[string]

# 敏感信息正则表达式
const SENSITIVE_PATTERNS = @[
  ("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}", "Email"),
  ("password|pwd|pass", "Password"),
  ("api.*key|apikey", "API Key"),
  ("secret.*key|secretkey", "Secret Key"),
  ("access.*token|accesstoken", "Access Token"),
  ("private.*key|privatekey", "Private Key"),
  ("database.*password|db.*password", "DB Password"),
  ("admin|administrator", "Admin Credential"),
  ("1[3-9]\\d{9}", "Phone Number"),
  ("\\d{15,19}", "Credit Card"),
  ("Bearer\\s+[A-Za-z0-9\\-\\._~\\+\\/]+=*", "Bearer Token")
]

proc extractLinks(html: string, baseUrl: string): seq[string] =
  ## 提取页面链接
  result = @[]

  # 简单的链接提取 (不使用正则表达式)
  var pos = 0
  while true:
    let hrefPos = html.find("href=", pos)
    if hrefPos == -1:
      break

    let quotePos = hrefPos + 5
    var quote = '"'
    if quotePos < html.len and html[quotePos] == '\'':
      quote = '\''
    elif quotePos < html.len and html[quotePos] == '"':
      quote = '"'
    else:
      pos = hrefPos + 5
      continue

    let linkStart = quotePos + 1
    let linkEnd = html.find(quote, linkStart)
    if linkEnd == -1:
      break

    var link = html[linkStart..<linkEnd]
    if link.startsWith("/"):
      link = baseUrl & link
    elif not link.startsWith("http"):
      link = baseUrl & "/" & link

    if link notin result and baseUrl in link:
      result.add(link)

    pos = linkEnd + 1

proc extractJSFiles(html: string, baseUrl: string): seq[string] =
  ## 提取JS文件
  result = @[]

  # 简单的JS文件提取
  var pos = 0
  while true:
    let srcPos = html.find("src=", pos)
    if srcPos == -1:
      break

    let quotePos = srcPos + 4
    var quote = '"'
    if quotePos < html.len and html[quotePos] == '\'':
      quote = '\''
    elif quotePos < html.len and html[quotePos] == '"':
      quote = '"'
    else:
      pos = srcPos + 4
      continue

    let fileStart = quotePos + 1
    let fileEnd = html.find(quote, fileStart)
    if fileEnd == -1:
      break

    let jsFile = html[fileStart..<fileEnd]
    if ".js" in jsFile:
      var fullPath = jsFile
      if fullPath.startsWith("/"):
        fullPath = baseUrl & fullPath
      elif not fullPath.startsWith("http"):
        fullPath = baseUrl & "/" & fullPath

      if fullPath notin result:
        result.add(fullPath)

    pos = fileEnd + 1

proc extractForms(html: string): seq[string] =
  ## 提取表单信息
  result = @[]

  # 简单的表单提取
  var pos = 0
  while true:
    let formStart = html.find("<form", pos)
    if formStart == -1:
      break

    let formEnd = html.find("</form>", formStart)
    if formEnd == -1:
      break

    let form = html[formStart..<formEnd + 7]
    result.add(form)
    pos = formEnd + 7

proc scanSensitiveInfo(content: string, location: string): seq[SensitiveInfo] =
  ## 扫描敏感信息
  result = @[]

  for (pattern, infoType) in SENSITIVE_PATTERNS:
    if pattern in content.toLower():
      # 简化的敏感信息检测
      let lines = content.splitLines()
      for line in lines:
        if pattern in line.toLower():
          result.add(SensitiveInfo(
            infoType: infoType,
            content: line.strip(),
            location: location
          ))
          break  # 每种类型只记录一次

proc crawlPage(url: string): Future[WebPage] {.async.} =
  ## 爬取单个页面
  var page = WebPage(url: url)

  try:
    await randomDelay()

    let curlProcess = startProcess("curl",
      args=["-s", "-I", "--connect-timeout", "10", "--max-time", "15",
            "-H", "User-Agent: " & getRandomUserAgent(),
            url],
      options={poUsePath})
    let headerOutput = curlProcess.outputStream.readAll()
    curlProcess.close()

    # 解析响应头
    let headerLines = headerOutput.splitLines()
    for line in headerLines:
      if line.startsWith("HTTP/"):
        let parts = line.split(" ")
        if parts.len >= 2:
          try:
            page.statusCode = parseInt(parts[1])
          except:
            page.statusCode = 0
      elif ":" in line:
        let parts = line.split(":", 1)
        if parts.len == 2:
          page.headers.add((parts[0].strip(), parts[1].strip()))

    # 获取页面内容
    if page.statusCode >= 200 and page.statusCode < 400:
      let contentProcess = startProcess("curl",
        args=["-s", "--connect-timeout", "10", "--max-time", "15",
              "-H", "User-Agent: " & getRandomUserAgent(),
              url],
        options={poUsePath})
      let content = contentProcess.outputStream.readAll()
      contentProcess.close()

      page.contentLength = content.len

      # 提取标题
      if "<title>" in content:
        let titleStart = content.find("<title>") + 7
        let titleEnd = content.find("</title>", titleStart)
        if titleEnd > titleStart:
          page.title = content[titleStart..<titleEnd].strip()

      # 提取链接、JS文件、表单
      page.links = extractLinks(content, url.split("/")[0..2].join("/"))
      page.jsFiles = extractJSFiles(content, url.split("/")[0..2].join("/"))
      page.forms = extractForms(content)

    logResult("WEB-CRAWL", "$1 [$2] $3 - $4" % [
      url, $page.statusCode, $page.contentLength, page.title])

  except Exception as e:
    logWarn("Failed to crawl $1: $2" % [url, e.msg])

  return page

proc scanJSFiles(jsFiles: seq[string]): Future[seq[SensitiveInfo]] {.async.} =
  ## 扫描JS文件中的敏感信息
  result = @[]

  logDebug("Scanning $1 JavaScript files..." % [$jsFiles.len])

  for jsFile in jsFiles:
    try:
      await randomDelay()

      let curlProcess = startProcess("curl",
        args=["-s", "--connect-timeout", "10", "--max-time", "15",
              "-H", "User-Agent: " & getRandomUserAgent(),
              jsFile],
        options={poUsePath})
      let jsContent = curlProcess.outputStream.readAll()
      curlProcess.close()

      if jsContent.len > 0:
        let sensitiveInfo = scanSensitiveInfo(jsContent, jsFile)
        result.add(sensitiveInfo)

        if sensitiveInfo.len > 0:
          logResult("JS-SENSITIVE", "Found $1 sensitive items in $2" % [
            $sensitiveInfo.len, jsFile])
          for info in sensitiveInfo:
            logResult("JS-$1" % [info.infoType.toUpper()], info.content)

    except Exception as e:
      logWarn("Failed to scan JS file $1: $2" % [jsFile, e.msg])

  return result

proc discoverAPIEndpoints(pages: seq[WebPage]): seq[APIEndpoint] =
  ## 发现API端点
  result = @[]

  let apiPatterns = @[
    "/api/",
    "/v1/", "/v2/", "/v3/",
    ".json",
    "/rest/",
    "/graphql"
  ]

  for page in pages:
    for link in page.links:
      for pattern in apiPatterns:
        if pattern in link:
          let endpoint = APIEndpoint(
            url: link,
            httpMethod: "GET",  # 默认GET，实际应该进一步检测
            parameters: @[]
          )

          # 检查是否已存在
          var exists = false
          for existing in result:
            if existing.url == endpoint.url:
              exists = true
              break

          if not exists:
            result.add(endpoint)
            logResult("API-ENDPOINT", link)

proc directoryBruteForce(baseUrl: string): Future[seq[string]] {.async.} =
  ## 目录爆破
  result = @[]

  let commonDirs = @[
    "admin", "administrator", "login", "panel", "dashboard",
    "api", "v1", "v2", "rest", "graphql",
    "backup", "backups", "bak", "old", "tmp",
    "test", "testing", "dev", "development",
    "config", "configuration", "settings",
    "upload", "uploads", "files", "assets",
    "images", "img", "css", "js", "static",
    "docs", "documentation", "help", "support"
  ]

  logDebug("Starting directory brute force...")

  for dir in commonDirs:
    let testUrl = baseUrl & "/" & dir

    try:
      let curlProcess = startProcess("curl",
        args=["-s", "-I", "--connect-timeout", "5", "--max-time", "10",
              "-H", "User-Agent: " & getRandomUserAgent(),
              testUrl],
        options={poUsePath})
      let output = curlProcess.outputStream.readAll()
      curlProcess.close()

      if "200 OK" in output or "301" in output or "302" in output:
        result.add(testUrl)
        logResult("DIR-FOUND", testUrl)

      await sleepAsync(200)  # 避免过于频繁的请求

    except:
      continue

  return result

proc scanWeb*(domain: string): Future[WebScanResult] {.async.} =
  ## 主Web扫描函数
  logInfo("Starting comprehensive web scanning for: " & domain)

  var result = WebScanResult(domain: domain)
  let baseUrl = "http://" & domain

  # 1. 爬取主页
  logDebug("Crawling main page...")
  let mainPage = await crawlPage(baseUrl)
  result.pages.add(mainPage)

  # 2. 爬取发现的链接 (限制数量)
  logDebug("Crawling discovered links...")
  var crawledUrls: seq[string] = @[baseUrl]
  var linksToCrawl = mainPage.links

  # 限制爬取数量避免无限循环
  if linksToCrawl.len > 20:
    linksToCrawl = linksToCrawl[0..<20]

  for link in linksToCrawl:
    if link notin crawledUrls:
      let page = await crawlPage(link)
      result.pages.add(page)
      crawledUrls.add(link)

      # 收集所有JS文件
      for jsFile in page.jsFiles:
        if jsFile notin result.jsFiles:
          result.jsFiles.add(jsFile)

  # 3. 扫描JS文件
  if result.jsFiles.len > 0:
    logDebug("Scanning JavaScript files for sensitive information...")
    let jsSensitiveInfo = await scanJSFiles(result.jsFiles)
    result.sensitiveInfo.add(jsSensitiveInfo)

  # 4. 发现API端点
  logDebug("Discovering API endpoints...")
  result.apiEndpoints = discoverAPIEndpoints(result.pages)

  # 5. 目录爆破
  logDebug("Performing directory brute force...")
  result.directories = await directoryBruteForce(baseUrl)

  # 6. 扫描所有页面内容中的敏感信息
  logDebug("Scanning page content for sensitive information...")
  for page in result.pages:
    # 这里应该重新获取页面内容进行敏感信息扫描
    # 简化实现，实际应该缓存页面内容
    discard

  logSuccess("Web scanning completed for: $1" % [domain])
  logInfo("Summary: $1 pages, $2 JS files, $3 API endpoints, $4 directories" % [
    $result.pages.len, $result.jsFiles.len,
    $result.apiEndpoints.len, $result.directories.len])

  return result
