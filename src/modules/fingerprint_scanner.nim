##[
  指纹识别和扫描模块
  实现指纹识别、目录探测、端口扫描、服务识别
]##

import asyncdispatch, httpclient, strutils, sequtils, osproc, streams, tables
import ../core/[logger, config]
import ../modules/evasion

type
  ServiceInfo* = object
    port*: int
    protocol*: string
    service*: string
    version*: string
    banner*: string

  WebFingerprint* = object
    technology*: string
    version*: string
    confidence*: float
    evidence*: seq[string]

  FingerprintResult* = object
    domain*: string
    ip*: string
    openPorts*: seq[ServiceInfo]
    webFingerprints*: seq[WebFingerprint]
    serverInfo*: string
    technologies*: seq[string]

# Web技术指纹库
const WEB_FINGERPRINTS = {
  "Apache": @[
    ("Server: Apache", 0.9),
    ("Apache/", 0.8),
    ("mod_", 0.6)
  ],
  "Nginx": @[
    ("Server: nginx", 0.9),
    ("nginx/", 0.8)
  ],
  "IIS": @[
    ("Server: Microsoft-IIS", 0.9),
    ("X-Powered-By: ASP.NET", 0.8)
  ],
  "PHP": @[
    ("X-Powered-By: PHP", 0.9),
    ("Set-Cookie: PHPSESSID", 0.8),
    ("<?php", 0.7)
  ],
  "ASP.NET": @[
    ("X-Powered-By: ASP.NET", 0.9),
    ("X-AspNet-Version", 0.8),
    ("__VIEWSTATE", 0.7)
  ],
  "Java": @[
    ("X-Powered-By: Servlet", 0.8),
    ("JSESSIONID", 0.7),
    ("Server: Apache-Coyote", 0.6)
  ],
  "WordPress": @[
    ("wp-content", 0.9),
    ("wp-includes", 0.8),
    ("WordPress", 0.7)
  ],
  "Drupal": @[
    ("Drupal", 0.9),
    ("sites/default/files", 0.8)
  ],
  "Joomla": @[
    ("Joomla", 0.9),
    ("option=com_", 0.7)
  ],
  "Spring": @[
    ("Spring Framework", 0.9),
    ("Whitelabel Error Page", 0.8)
  ],
  "Laravel": @[
    ("laravel_session", 0.9),
    ("Laravel", 0.8)
  ],
  "Django": @[
    ("Django", 0.9),
    ("csrfmiddlewaretoken", 0.8)
  ],
  "React": @[
    ("react", 0.8),
    ("__REACT_DEVTOOLS", 0.7)
  ],
  "Vue.js": @[
    ("Vue.js", 0.9),
    ("vue", 0.7)
  ],
  "jQuery": @[
    ("jquery", 0.8),
    ("jQuery", 0.8)
  ]
}.toTable()

proc scanPorts(target: string): Future[seq[ServiceInfo]] {.async.} =
  ## 端口扫描
  result = @[]

  logDebug("Starting port scan for: " & target)

  try:
    # 使用nmap进行端口扫描
    let nmapProcess = startProcess("nmap",
      args=["-sS", "-sV", "-O", "--top-ports", "1000",
            "--max-retries", "2", "--host-timeout", "300s", target],
      options={poUsePath})
    let output = nmapProcess.outputStream.readAll()
    nmapProcess.close()

    let lines = output.splitLines()
    for line in lines:
      if "/tcp" in line and "open" in line:
        let parts = line.split()
        if parts.len >= 3:
          try:
            let portStr = parts[0].split("/")[0]
            let port = parseInt(portStr)
            let service = if parts.len > 2: parts[2] else: "unknown"
            let version = if parts.len > 3: parts[3..^1].join(" ") else: ""

            result.add(ServiceInfo(
              port: port,
              protocol: "tcp",
              service: service,
              version: version,
              banner: ""
            ))

            logResult("PORT-OPEN", "$1:$2 ($3) $4" % [target, $port, service, version])
          except:
            continue

    logResult("PORT-SCAN", "Found $1 open ports" % [$result.len])

  except Exception as e:
    logWarn("Port scan failed: " & e.msg)

    # 备用方案：扫描常见端口
    let commonPorts = @[21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995,
                       1433, 3306, 3389, 5432, 6379, 8080, 8443, 9200]

    logDebug("Falling back to common port scan...")
    for port in commonPorts:
      try:
        let ncProcess = startProcess("nc",
          args=["-z", "-v", "-w", "3", target, $port],
          options={poUsePath})
        let ncOutput = ncProcess.outputStream.readAll()
        let exitCode = ncProcess.waitForExit()
        ncProcess.close()

        if exitCode == 0 or "succeeded" in ncOutput:
          result.add(ServiceInfo(
            port: port,
            protocol: "tcp",
            service: "unknown",
            version: "",
            banner: ""
          ))
          logResult("PORT-OPEN", "$1:$2" % [target, $port])
      except:
        continue

  return result

proc identifyWebTechnologies(domain: string): Future[seq[WebFingerprint]] {.async.} =
  ## Web技术指纹识别
  result = @[]

  logDebug("Identifying web technologies for: " & domain)

  try:
    # 获取HTTP响应头和页面内容
    let curlProcess = startProcess("curl",
      args=["-s", "-I", "--connect-timeout", "10", "--max-time", "15",
            "-H", "User-Agent: " & getRandomUserAgent(),
            "http://" & domain],
      options={poUsePath})
    let headers = curlProcess.outputStream.readAll()
    curlProcess.close()

    let contentProcess = startProcess("curl",
      args=["-s", "--connect-timeout", "10", "--max-time", "15",
            "-H", "User-Agent: " & getRandomUserAgent(),
            "http://" & domain],
      options={poUsePath})
    let content = contentProcess.outputStream.readAll()
    contentProcess.close()

    let fullResponse = headers & "\n" & content

    # 检查每个技术指纹
    for technology, patterns in WEB_FINGERPRINTS:
      var fingerprint = WebFingerprint(
        technology: technology,
        confidence: 0.0,
        evidence: @[]
      )

      for (pattern, confidence) in patterns:
        if pattern.toLower() in fullResponse.toLower():
          fingerprint.confidence += confidence
          fingerprint.evidence.add(pattern)

      # 如果置信度足够高，添加到结果
      if fingerprint.confidence >= 0.6:
        # 尝试提取版本信息
        if technology == "Apache" and "Apache/" in fullResponse:
          let apacheStart = fullResponse.find("Apache/") + 7
          let apacheEnd = fullResponse.find(" ", apacheStart)
          if apacheEnd > apacheStart:
            fingerprint.version = fullResponse[apacheStart..<apacheEnd]
        elif technology == "Nginx" and "nginx/" in fullResponse:
          let nginxStart = fullResponse.find("nginx/") + 6
          let nginxEnd = fullResponse.find(" ", nginxStart)
          if nginxEnd > nginxStart:
            fingerprint.version = fullResponse[nginxStart..<nginxEnd]
        elif technology == "PHP" and "PHP/" in fullResponse:
          let phpStart = fullResponse.find("PHP/") + 4
          let phpEnd = fullResponse.find(" ", phpStart)
          if phpEnd > phpStart:
            fingerprint.version = fullResponse[phpStart..<phpEnd]

        result.add(fingerprint)

        let versionStr = if fingerprint.version != "": " " & fingerprint.version else: ""
        logResult("WEB-TECH", "$1$2 (confidence: $3)" % [
          technology, versionStr, $(fingerprint.confidence)])

  except Exception as e:
    logWarn("Web technology identification failed: " & e.msg)

  return result

proc getServerInfo(domain: string): Future[string] {.async.} =
  ## 获取服务器信息
  try:
    let curlProcess = startProcess("curl",
      args=["-s", "-I", "--connect-timeout", "10",
            "-H", "User-Agent: " & getRandomUserAgent(),
            "http://" & domain],
      options={poUsePath})
    let output = curlProcess.outputStream.readAll()
    curlProcess.close()

    let lines = output.splitLines()
    for line in lines:
      if line.toLower().startsWith("server:"):
        let serverInfo = line.split(":", 1)[1].strip()
        logResult("SERVER-INFO", serverInfo)
        return serverInfo

    return "Unknown"
  except:
    return "Unknown"

proc scanFingerprint*(domain: string): Future[FingerprintResult] {.async.} =
  ## 主指纹识别函数
  logInfo("Starting fingerprint scanning for: " & domain)

  var result = FingerprintResult(domain: domain)

  # 解析域名获取IP
  try:
    let digProcess = startProcess("dig", args=["+short", domain], options={poUsePath})
    let output = digProcess.outputStream.readAll()
    digProcess.close()

    for line in output.splitLines():
      let ip = line.strip()
      if ip.len > 0 and "." in ip:
        result.ip = ip
        break
  except:
    result.ip = "Unknown"

  logResult("TARGET-IP", "$1 -> $2" % [domain, result.ip])

  # 1. 端口扫描
  if result.ip != "Unknown":
    logDebug("Scanning ports...")
    result.openPorts = await scanPorts(result.ip)

  # 2. Web技术识别
  logDebug("Identifying web technologies...")
  result.webFingerprints = await identifyWebTechnologies(domain)

  # 3. 服务器信息
  logDebug("Getting server information...")
  result.serverInfo = await getServerInfo(domain)

  # 4. 汇总技术栈
  for fingerprint in result.webFingerprints:
    if fingerprint.technology notin result.technologies:
      result.technologies.add(fingerprint.technology)

  logSuccess("Fingerprint scanning completed for: $1" % [domain])
  logInfo("Summary: $1 open ports, $2 technologies identified" % [
    $result.openPorts.len, $result.technologies.len])

  return result
