##[
  配置管理模块
  管理工具的全局配置参数
]##

import os, strutils, parsecfg

type
  KaiTianConfig* = object
    # 基础配置
    logLevel*: string
    outputDir*: string
    maxConcurrency*: int
    requestTimeout*: int
    
    # 反检测配置
    userAgents*: seq[string]
    proxyList*: seq[string]
    requestDelay*: tuple[min: int, max: int]
    
    # API配置
    dnsHistoryAPIs*: seq[string]
    searchEngineAPIs*: seq[string]
    
    # 扫描配置
    subdomainWordlist*: string
    directoryWordlist*: string
    portRange*: string
    
    # 输出配置
    enableRealTimeOutput*: bool
    reportFormat*: string

const DEFAULT_CONFIG = """
[basic]
log_level = "INFO"
output_dir = "./output"
max_concurrency = 50
request_timeout = 10

[evasion]
user_agents = [
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
]
proxy_list = []
request_delay_min = 1
request_delay_max = 3

[apis]
dns_history_apis = [
  "https://api.hackertarget.com/hostsearch/?q=",
  "https://crt.sh/?q=%25.{domain}&output=json"
]

[scanning]
subdomain_wordlist = "./wordlists/subdomains.txt"
directory_wordlist = "./wordlists/directories.txt"
port_range = "1-65535"

[output]
enable_real_time_output = true
report_format = "json"
"""

proc loadConfig*(): KaiTianConfig =
  # 使用默认配置，简化实现
  result = KaiTianConfig(
    logLevel: "INFO",
    outputDir: "./output",
    maxConcurrency: 50,
    requestTimeout: 10,

    userAgents: @[
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
      "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
    ],
    requestDelay: (min: 1, max: 3),

    dnsHistoryAPIs: @[
      "https://api.hackertarget.com/hostsearch/?q=",
      "https://crt.sh/?q=%25.{domain}&output=json"
    ],

    subdomainWordlist: "./wordlists/subdomains.txt",
    directoryWordlist: "./wordlists/directories.txt",
    portRange: "1-65535",

    enableRealTimeOutput: true,
    reportFormat: "json"
  )
