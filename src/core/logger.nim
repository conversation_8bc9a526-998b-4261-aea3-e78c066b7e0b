##[
  日志管理模块
  提供彩色日志输出和文件记录
]##

import times, strutils, os
import terminal

type
  LogLevel* = enum
    DEBUG, INFO, WARN, ERROR, CRITICAL

var currentLogLevel: LogLevel = INFO

proc initLogger*(level: string) =
  case level.toUpper():
    of "DEBUG": currentLogLevel = DEBUG
    of "INFO": currentLogLevel = INFO  
    of "WARN": currentLogLevel = WARN
    of "ERROR": currentLogLevel = ERROR
    of "CRITICAL": currentLogLevel = CRITICAL
    else: currentLogLevel = INFO

proc formatTime(): string =
  now().format("yyyy-MM-dd HH:mm:ss")

proc logMessage(level: LogLevel, msg: string, color: ForegroundColor) =
  if level >= currentLogLevel:
    let timestamp = formatTime()
    let levelStr = $level
    
    # 控制台彩色输出
    stdout.styledWrite(fgBlack, "[", timestamp, "] ")
    stdout.styledWrite(color, styleBright, "[", levelStr, "] ")
    stdout.styledWrite(fgWhite, msg, "\n")
    
    # 文件日志记录
    let logFile = "kaitian.log"
    let logEntry = "[$1] [$2] $3\n" % [timestamp, levelStr, msg]
    writeFile(logFile, logEntry)

proc logDebug*(msg: string) =
  logMessage(DEBUG, msg, fgCyan)

proc logInfo*(msg: string) =
  logMessage(INFO, msg, fgGreen)

proc logWarn*(msg: string) =
  logMessage(WARN, msg, fgYellow)

proc logError*(msg: string) =
  logMessage(ERROR, msg, fgRed)

proc logCritical*(msg: string) =
  logMessage(CRITICAL, msg, fgMagenta)

# 特殊的实时输出函数，用于扫描结果
proc logResult*(category: string, content: string) =
  let timestamp = formatTime()
  stdout.styledWrite(fgBlack, "[", timestamp, "] ")
  stdout.styledWrite(fgCyan, styleBright, "[", category, "] ")
  stdout.styledWrite(fgWhite, content, "\n")

proc logSuccess*(msg: string) =
  let timestamp = formatTime()
  stdout.styledWrite(fgBlack, "[", timestamp, "] ")
  stdout.styledWrite(fgGreen, styleBright, "[SUCCESS] ")
  stdout.styledWrite(fgWhite, msg, "\n")

proc logTarget*(target: string) =
  echo ""
  stdout.styledWrite(fgYellow, styleBright, "=" .repeat(60), "\n")
  stdout.styledWrite(fgYellow, styleBright, "TARGET: ", fgWhite, target, "\n")
  stdout.styledWrite(fgYellow, styleBright, "=" .repeat(60), "\n")
  echo ""
