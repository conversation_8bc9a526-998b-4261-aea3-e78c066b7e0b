##[
  Banner显示模块
  显示工具启动横幅
]##

import terminal, strutils

const BANNER = """
██╗  ██╗ █████╗ ██╗████████╗██╗ █████╗ ███╗   ██╗
██║ ██╔╝██╔══██╗██║╚══██╔══╝██║██╔══██╗████╗  ██║
█████╔╝ ███████║██║   ██║   ██║███████║██╔██╗ ██║
██╔═██╗ ██╔══██║██║   ██║   ██║██╔══██║██║╚██╗██║
██║  ██╗██║  ██║██║   ██║   ██║██║  ██║██║ ╚████║
╚═╝  ╚═╝╚═╝  ╚═╝╚═╝   ╚═╝   ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝

    开天 - 全自动渗透测试工具 v1.0.0
    Automated Penetration Testing Framework
    
    Author: KaiTian Security Team
    GitHub: https://github.com/kaitian-security/kaitian
"""

const FEATURES = """
[+] CDN检测与绕过
[+] DNS历史记录查询  
[+] 全面信息收集
[+] 子域名收集 (被动+主动)
[+] Web爬虫与JS分析
[+] 指纹识别与漏洞扫描
[+] 反检测与WAF绕过
[+] 实时结果输出
[+] 高并发异步处理
"""

proc showBanner*() =
  # 清屏
  eraseScreen()
  setCursorPos(0, 0)
  
  # 显示主Banner
  stdout.styledWrite(fgRed, styleBright, BANNER)
  echo ""
  
  # 显示功能特性
  stdout.styledWrite(fgCyan, FEATURES)
  echo ""
  
  # 显示分隔线
  stdout.styledWrite(fgYellow, "=" .repeat(60))
  echo ""
  echo ""

proc showModuleHeader*(moduleName: string, description: string) =
  echo ""
  stdout.styledWrite(fgMagenta, styleBright, "┌─ ", moduleName, " ─┐")
  echo ""
  stdout.styledWrite(fgWhite, "│ ", description)
  echo ""
  stdout.styledWrite(fgMagenta, styleBright, "└", "─".repeat(moduleName.len + 4), "┘")
  echo ""
