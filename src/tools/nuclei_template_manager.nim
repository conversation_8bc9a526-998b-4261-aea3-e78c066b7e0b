##[
  Nuclei模板管理工具
  下载、解析和管理Nuclei模板库
]##

import asyncdispatch, httpclient, json, strutils, os, tables, sequtils
import ../core/logger

type
  TemplateStats* = object
    totalTemplates*: int
    cveTemplates*: int
    exposureTemplates*: int
    misconfigTemplates*: int
    rceTemplates*: int
    xssTemplates*: int
    sqliTemplates*: int
    
  DownloadResult* = object
    success*: bool
    templatesDownloaded*: int
    error*: string

const NUCLEI_TEMPLATE_SOURCES = @[
  "https://api.github.com/repos/projectdiscovery/nuclei-templates/contents/http/cves",
  "https://api.github.com/repos/projectdiscovery/nuclei-templates/contents/http/exposures", 
  "https://api.github.com/repos/projectdiscovery/nuclei-templates/contents/http/misconfiguration",
  "https://api.github.com/repos/projectdiscovery/nuclei-templates/contents/http/vulnerabilities"
]

proc downloadTemplateList(url: string): Future[seq[string]] {.async.} =
  ## 下载模板列表
  result = @[]
  
  var client = newAsyncHttpClient()
  defer: client.close()
  
  try:
    client.headers = newHttpHeaders({
      "User-Agent": "KaiTian-TemplateManager/1.0",
      "Accept": "application/vnd.github.v3+json"
    })
    
    logInfo("Downloading template list from: " & url)
    let response = await client.getContent(url)
    let jsonData = parseJson(response)
    
    for item in jsonData:
      if item.hasKey("name") and item.hasKey("download_url"):
        let name = item["name"].getStr()
        if name.endsWith(".yaml") or name.endsWith(".yml"):
          let downloadUrl = item["download_url"].getStr()
          result.add(downloadUrl)
    
    logSuccess("Found $1 templates in directory" % [$result.len])
    
  except Exception as e:
    logError("Failed to download template list: " & e.msg)

proc downloadTemplate(url: string): Future[string] {.async.} =
  ## 下载单个模板
  var client = newAsyncHttpClient()
  defer: client.close()
  
  try:
    client.headers = newHttpHeaders({
      "User-Agent": "KaiTian-TemplateManager/1.0"
    })
    
    let content = await client.getContent(url)
    return content
    
  except Exception as e:
    logWarn("Failed to download template from $1: $2" % [url, e.msg])
    return ""

proc parseNucleiTemplate(yamlContent: string): tuple[valid: bool, templateInfo: string] =
  ## 解析Nuclei YAML模板 (简化版)
  try:
    let lines = yamlContent.splitLines()
    var templateId = ""
    var templateName = ""
    var severity = ""
    var tags: seq[string] = @[]
    
    for line in lines:
      let trimmedLine = line.strip()
      if trimmedLine.startsWith("id:"):
        templateId = trimmedLine.split(":", 1)[1].strip()
      elif trimmedLine.startsWith("name:"):
        templateName = trimmedLine.split(":", 1)[1].strip()
      elif trimmedLine.startsWith("severity:"):
        severity = trimmedLine.split(":", 1)[1].strip()
      elif trimmedLine.startsWith("tags:"):
        let tagLine = trimmedLine.split(":", 1)[1].strip()
        if tagLine.startsWith("[") and tagLine.endsWith("]"):
          let tagContent = tagLine[1..^2]
          tags = tagContent.split(",").mapIt(it.strip())
    
    if templateId != "" and templateName != "":
      let info = "ID: $1, Name: $2, Severity: $3, Tags: $4" % [
        templateId, templateName, severity, tags.join(",")]
      return (valid: true, templateInfo: info)
    else:
      return (valid: false, templateInfo: "")
      
  except Exception as e:
    return (valid: false, templateInfo: "Parse error: " & e.msg)

proc generateNimTemplate(yamlContent: string, templateId: string): string =
  ## 将YAML模板转换为Nim代码格式
  result = "  # Auto-generated from Nuclei template: $1\n" % [templateId]
  result.add("  NucleiTemplate(\n")
  result.add("    id: \"$1\",\n" % [templateId])
  
  # 简化的转换逻辑
  let lines = yamlContent.splitLines()
  for line in lines:
    let trimmedLine = line.strip()
    if trimmedLine.startsWith("name:"):
      let name = trimmedLine.split(":", 1)[1].strip().replace("\"", "\\\"")
      result.add("    name: \"$1\",\n" % [name])
    elif trimmedLine.startsWith("severity:"):
      let severity = trimmedLine.split(":", 1)[1].strip()
      result.add("    severity: \"$1\",\n" % [severity])
  
  result.add("    # TODO: Complete template conversion\n")
  result.add("  ),\n")

proc downloadNucleiTemplates*(): Future[DownloadResult] {.async.} =
  ## 下载Nuclei模板库
  var result = DownloadResult(success: false, templatesDownloaded: 0)
  
  logInfo("Starting Nuclei template download...")
  
  var allTemplateUrls: seq[string] = @[]
  
  # 下载所有目录的模板列表
  for sourceUrl in NUCLEI_TEMPLATE_SOURCES:
    let templateUrls = await downloadTemplateList(sourceUrl)
    allTemplateUrls.add(templateUrls)
  
  logInfo("Total templates found: $1" % [$allTemplateUrls.len])
  
  # 创建输出目录
  let outputDir = "nuclei_templates"
  if not dirExists(outputDir):
    createDir(outputDir)
  
  var downloadedCount = 0
  var validTemplates: seq[string] = @[]
  
  # 下载前50个模板作为示例 (避免过多请求)
  let maxDownloads = min(50, allTemplateUrls.len)
  
  for i in 0..<maxDownloads:
    let templateUrl = allTemplateUrls[i]
    let templateContent = await downloadTemplate(templateUrl)
    
    if templateContent != "":
      let parseResult = parseNucleiTemplate(templateContent)
      if parseResult.valid:
        downloadedCount += 1
        validTemplates.add(parseResult.templateInfo)
        
        # 保存模板文件
        let fileName = templateUrl.split("/")[^1]
        let filePath = outputDir / fileName
        writeFile(filePath, templateContent)
        
        logResult("TEMPLATE-DOWNLOADED", parseResult.templateInfo)
      else:
        logWarn("Invalid template: " & templateUrl)
    
    # 延迟避免API限制
    await sleepAsync(100)
    
    if downloadedCount mod 10 == 0:
      logDebug("Download progress: $1/$2" % [$downloadedCount, $maxDownloads])
  
  # 生成统计信息
  logInfo("Template download completed:")
  logInfo("  - Total downloaded: $1" % [$downloadedCount])
  logInfo("  - Valid templates: $1" % [$validTemplates.len])
  logInfo("  - Saved to: $1" % [outputDir])
  
  result.success = true
  result.templatesDownloaded = downloadedCount
  
  return result

proc generateTemplateStats*(templateDir: string): TemplateStats =
  ## 生成模板统计信息
  result = TemplateStats()
  
  if not dirExists(templateDir):
    logWarn("Template directory not found: " & templateDir)
    return result
  
  for file in walkFiles(templateDir / "*.yaml"):
    result.totalTemplates += 1
    
    let content = readFile(file).toLower()
    
    # 简单的分类统计
    if "cve-" in content:
      result.cveTemplates += 1
    if "exposure" in content or "disclosure" in content:
      result.exposureTemplates += 1
    if "misconfiguration" in content or "misconfig" in content:
      result.misconfigTemplates += 1
    if "rce" in content or "remote code" in content:
      result.rceTemplates += 1
    if "xss" in content or "cross-site" in content:
      result.xssTemplates += 1
    if "sqli" in content or "sql injection" in content:
      result.sqliTemplates += 1

proc showTemplateStats*() =
  ## 显示模板统计信息
  let stats = generateTemplateStats("nuclei_templates")
  
  logInfo("📊 Nuclei Template Statistics:")
  logInfo("  - Total templates: $1" % [$stats.totalTemplates])
  logInfo("  - CVE templates: $1" % [$stats.cveTemplates])
  logInfo("  - Exposure templates: $1" % [$stats.exposureTemplates])
  logInfo("  - Misconfiguration templates: $1" % [$stats.misconfigTemplates])
  logInfo("  - RCE templates: $1" % [$stats.rceTemplates])
  logInfo("  - XSS templates: $1" % [$stats.xssTemplates])
  logInfo("  - SQL Injection templates: $1" % [$stats.sqliTemplates])

# 命令行工具入口
when isMainModule:
  proc main() {.async.} =
    echo "🔧 KaiTian Nuclei Template Manager"
    echo "=================================="
    
    showTemplateStats()
    echo ""
    
    let result = await downloadNucleiTemplates()
    echo ""
    
    if result.success:
      echo "✅ Template download completed successfully!"
      echo "   Downloaded: $1 templates" % [$result.templatesDownloaded]
      echo ""
      showTemplateStats()
    else:
      echo "❌ Template download failed: $1" % [result.error]
  
  waitFor main()
