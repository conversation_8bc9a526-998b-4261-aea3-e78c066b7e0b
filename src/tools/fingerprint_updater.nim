##[
  指纹库更新工具
  从GitHub自动下载和更新指纹库
]##

import asyncdispatch, httpclient, json, strutils, os, tables, sequtils
import ../core/logger

type
  FingerprintSource* = object
    name*: string
    url*: string
    format*: string  # json, yaml, etc
    
  UpdateResult* = object
    source*: string
    success*: bool
    newRules*: int
    totalRules*: int
    error*: string

# 指纹库源列表
const FINGERPRINT_SOURCES = @[
  FingerprintSource(
    name: "Wappalyzer",
    url: "https://raw.githubusercontent.com/projectdiscovery/wappalyzergo/main/fingerprints_data.json",
    format: "json"
  ),
  FingerprintSource(
    name: "Nuclei Templates",
    url: "https://api.github.com/repos/projectdiscovery/nuclei-templates/contents/technologies",
    format: "github_api"
  ),
  FingerprintSource(
    name: "WebAnalyzer",
    url: "https://raw.githubusercontent.com/rverton/webanalyze/master/technologies.json",
    format: "json"
  ),
  FingerprintSource(
    name: "Retire.js",
    url: "https://raw.githubusercontent.com/RetireJS/retire.js/master/repository/jsrepository.json",
    format: "json"
  )
]

proc downloadFingerprintData(source: FingerprintSource): Future[string] {.async.} =
  ## 下载指纹数据
  var client = newAsyncHttpClient()
  defer: client.close()
  
  try:
    client.headers = newHttpHeaders({
      "User-Agent": "KaiTian-FingerprintUpdater/1.0",
      "Accept": "application/json"
    })
    
    logInfo("Downloading fingerprints from: " & source.name)
    let response = await client.getContent(source.url)
    logSuccess("Downloaded $1 bytes from $2" % [$response.len, source.name])
    return response
    
  except Exception as e:
    logError("Failed to download from $1: $2" % [source.name, e.msg])
    return ""

proc parseWappalyzerData(data: string): seq[tuple[app: string, rules: seq[string]]] =
  ## 解析Wappalyzer格式数据
  result = @[]
  
  try:
    let jsonData = parseJson(data)
    if not jsonData.hasKey("apps"):
      return result
    
    let apps = jsonData["apps"]
    for appName, appData in apps:
      var rules: seq[string] = @[]
      
      # 解析各种规则类型
      if appData.hasKey("headers"):
        for key, value in appData["headers"]:
          rules.add("headers:" & key & ":" & value.getStr())
      
      if appData.hasKey("html"):
        for pattern in appData["html"]:
          rules.add("html:" & pattern.getStr())
      
      if appData.hasKey("js"):
        for key, value in appData["js"]:
          rules.add("js:" & key & ":" & value.getStr())
      
      if appData.hasKey("cookies"):
        for key, value in appData["cookies"]:
          rules.add("cookies:" & key & ":" & value.getStr())
      
      if appData.hasKey("meta"):
        for key, value in appData["meta"]:
          rules.add("meta:" & key & ":" & value.getStr())
      
      if appData.hasKey("scriptSrc"):
        for pattern in appData["scriptSrc"]:
          rules.add("scriptSrc:" & pattern.getStr())
      
      if rules.len > 0:
        result.add((app: appName, rules: rules))
    
    var totalRules = 0
    for item in result:
      totalRules += item.rules.len
    logInfo("Parsed $1 applications with $2 total rules" % [$result.len, $totalRules])
    
  except Exception as e:
    logError("Failed to parse Wappalyzer data: " & e.msg)

proc generateNimCode(fingerprintData: seq[tuple[app: string, rules: seq[string]]]): string =
  ## 生成Nim代码格式的指纹库
  result = "# Auto-generated fingerprint rules\n"
  result.add("# Generated by KaiTian Fingerprint Updater\n\n")
  result.add("const AUTO_GENERATED_FINGERPRINTS* = @[\n")
  
  for appData in fingerprintData:
    let appName = appData.app
    for rule in appData.rules:
      let parts = rule.split(":", 2)
      if parts.len >= 2:
        let ruleType = parts[0]
        let pattern = if parts.len == 3: parts[2] else: parts[1]
        
        # 转义特殊字符
        let escapedPattern = pattern.replace("\\", "\\\\").replace("\"", "\\\"")
        
        result.add("  WappalyzerRule(appName: \"$1\", ruleType: \"$2\", pattern: \"$3\", confidence: 0.8, version: \"\"),\n" % [
          appName, ruleType, escapedPattern])
  
  result.add("]\n")

proc saveUpdatedFingerprints(code: string, filename: string): bool =
  ## 保存更新的指纹库
  try:
    writeFile(filename, code)
    logSuccess("Saved updated fingerprints to: " & filename)
    return true
  except Exception as e:
    logError("Failed to save fingerprints: " & e.msg)
    return false

proc updateFingerprintDatabase*(): Future[seq[UpdateResult]] {.async.} =
  ## 更新指纹数据库主函数
  result = @[]
  
  logInfo("Starting fingerprint database update...")
  
  for source in FINGERPRINT_SOURCES:
    var updateResult = UpdateResult(source: source.name, success: false)
    
    # 下载数据
    let data = await downloadFingerprintData(source)
    if data.len == 0:
      updateResult.error = "Download failed"
      result.add(updateResult)
      continue
    
    # 解析数据
    case source.format:
      of "json":
        if source.name == "Wappalyzer":
          let fingerprintData = parseWappalyzerData(data)
          if fingerprintData.len > 0:
            # 生成代码
            let nimCode = generateNimCode(fingerprintData)
            
            # 保存文件
            let filename = "src/data/auto_fingerprints_" & source.name.toLower() & ".nim"
            if saveUpdatedFingerprints(nimCode, filename):
              updateResult.success = true
              var totalRules = 0
              for item in fingerprintData:
                totalRules += item.rules.len
              updateResult.newRules = totalRules
              updateResult.totalRules = updateResult.newRules
            else:
              updateResult.error = "Save failed"
          else:
            updateResult.error = "Parse failed"
        else:
          # 其他JSON格式的处理
          updateResult.error = "Format not implemented yet"
      else:
        updateResult.error = "Unsupported format: " & source.format
    
    result.add(updateResult)
  
  # 输出更新结果
  logInfo("Fingerprint database update completed:")
  for res in result:
    if res.success:
      logSuccess("✅ $1: $2 rules updated" % [res.source, $res.newRules])
    else:
      logError("❌ $1: $2" % [res.source, res.error])

proc showFingerprintStats*() =
  ## 显示当前指纹库统计
  logInfo("Current Fingerprint Database Statistics:")
  logInfo("📊 Fingerprint updater ready")
  logInfo("🎯 Supported sources: $1" % [$FINGERPRINT_SOURCES.len])

  for source in FINGERPRINT_SOURCES:
    logInfo("  - $1: $2" % [source.name, source.url])

# 命令行工具入口
when isMainModule:
  proc main() {.async.} =
    echo "🔍 KaiTian Fingerprint Database Updater"
    echo "======================================="
    
    showFingerprintStats()
    echo ""
    
    let results = await updateFingerprintDatabase()
    echo ""
    
    var successCount = 0
    var totalRules = 0
    
    for result in results:
      if result.success:
        successCount += 1
        totalRules += result.newRules
    
    echo "📈 Update Summary:"
    echo "  - Sources updated: $1/$2" % [$successCount, $results.len]
    echo "  - Total new rules: $1" % [$totalRules]
    echo "  - Update completed successfully!"
  
  waitFor main()
