##[
  交互式界面模块
  提供命令行交互界面和实时结果显示
]##

import asyncdispatch, strutils, sequtils, terminal, os, times
import ../core/[logger, banner, config]
import ../modules/[cdn_detector, dns_history, info_collector, subdomain_collector]

type
  ScanTarget* = object
    domain*: string
    hasCDN*: bool
    realIP*: string
    subdomains*: seq[string]
    
  ScanSession* = object
    targets*: seq[ScanTarget]
    currentTarget*: int
    startTime*: float

var currentSession: ScanSession

proc showMenu() =
  echo ""
  stdout.styledWrite(fgCyan, styleBright, "┌─ 开天 主菜单 ─┐")
  echo ""
  stdout.styledWrite(fgWhite, "│ 1. 单域名扫描")
  echo ""
  stdout.styledWrite(fgWhite, "│ 2. 批量域名扫描")
  echo ""
  stdout.styledWrite(fgWhite, "│ 3. 查看扫描历史")
  echo ""
  stdout.styledWrite(fgWhite, "│ 4. 配置设置")
  echo ""
  stdout.styledWrite(fgWhite, "│ 5. 帮助信息")
  echo ""
  stdout.styledWrite(fgWhite, "│ 0. 退出程序")
  echo ""
  stdout.styledWrite(fgCyan, styleBright, "└─────────────┘")
  echo ""
  stdout.styledWrite(fgYellow, "请选择操作: ")

proc readInput(): string =
  result = readLine(stdin).strip()

proc validateDomain(domain: string): bool =
  ## 验证域名格式
  if domain.len == 0:
    return false
  
  # 简单的域名格式验证
  if not ("." in domain):
    return false
  
  # 检查是否包含协议
  if domain.startsWith("http://") or domain.startsWith("https://"):
    return false
  
  return true

proc parseDomainList(input: string): seq[string] =
  ## 解析域名列表
  result = @[]
  
  # 支持多种分隔符
  var domains: seq[string]
  if "," in input:
    domains = input.split(",")
  elif ";" in input:
    domains = input.split(";")
  elif " " in input:
    domains = input.split(" ")
  else:
    domains = @[input]
  
  for domain in domains:
    let cleanDomain = domain.strip()
    if validateDomain(cleanDomain):
      result.add(cleanDomain)

proc showProgress(current: int, total: int, message: string) =
  let percentage = (current * 100) div total
  let barLength = 30
  let filledLength = (percentage * barLength) div 100
  
  var bar = "["
  for i in 0..<barLength:
    if i < filledLength:
      bar.add("█")
    else:
      bar.add("░")
  bar.add("]")
  
  stdout.write("\r")
  stdout.styledWrite(fgGreen, bar)
  stdout.styledWrite(fgWhite, " $1% - $2" % [$percentage, message])
  stdout.flushFile()

proc performFullScan(domain: string): Future[ScanTarget] {.async.} =
  ## 执行完整扫描流程
  logTarget(domain)
  
  var target = ScanTarget(domain: domain)
  
  # 步骤1: CDN检测
  showModuleHeader("CDN检测", "检测目标是否使用CDN服务")
  let cdnResult = await detectCDN(domain)
  target.hasCDN = cdnResult.hasCDN
  
  if cdnResult.hasCDN:
    logWarn("检测到CDN: " & cdnResult.cdnProvider)
    
    # 步骤2: DNS历史记录查询
    showModuleHeader("DNS历史查询", "查找真实IP地址")
    let realIP = await findRealIP(domain)
    if realIP != "":
      target.realIP = realIP
      logSuccess("发现真实IP: " & realIP)
    else:
      logWarn("未能找到真实IP")
  else:
    logInfo("未检测到CDN，直接进行后续扫描")
  
  # 步骤3: 信息收集
  showModuleHeader("信息收集", "收集目标详细信息")
  # TODO: 实现信息收集
  
  # 步骤4: 子域名收集
  showModuleHeader("子域名收集", "收集子域名和相关IP")
  # TODO: 实现子域名收集
  
  # 步骤5: Web探测
  showModuleHeader("Web探测", "爬虫和漏洞扫描")
  # TODO: 实现Web探测
  
  return target

proc singleDomainScan(): Future[void] {.async.} =
  ## 单域名扫描
  echo ""
  stdout.styledWrite(fgCyan, styleBright, "=== 单域名扫描 ===")
  echo ""
  stdout.styledWrite(fgWhite, "请输入目标域名 (例: example.com): ")
  
  let input = readInput()
  if not validateDomain(input):
    logError("无效的域名格式")
    return
  
  let startTime = cpuTime()
  let target = await performFullScan(input)
  let endTime = cpuTime()
  
  # 显示扫描结果摘要
  echo ""
  stdout.styledWrite(fgGreen, styleBright, "=== 扫描完成 ===")
  echo ""
  stdout.styledWrite(fgWhite, "目标域名: ", fgYellow, target.domain)
  echo ""
  stdout.styledWrite(fgWhite, "CDN状态: ")
  if target.hasCDN:
    stdout.styledWrite(fgRed, "检测到CDN")
  else:
    stdout.styledWrite(fgGreen, "无CDN")
  echo ""
  
  if target.realIP != "":
    stdout.styledWrite(fgWhite, "真实IP: ", fgCyan, target.realIP)
    echo ""
  
  stdout.styledWrite(fgWhite, "扫描耗时: ", fgYellow, $(endTime - startTime), " 秒")
  echo ""

proc batchDomainScan(): Future[void] {.async.} =
  ## 批量域名扫描
  echo ""
  stdout.styledWrite(fgCyan, styleBright, "=== 批量域名扫描 ===")
  echo ""
  stdout.styledWrite(fgWhite, "请输入域名列表 (用逗号、分号或空格分隔): ")
  
  let input = readInput()
  let domains = parseDomainList(input)
  
  if domains.len == 0:
    logError("未找到有效域名")
    return
  
  logInfo("准备扫描 $1 个域名" % [$domains.len])
  
  currentSession = ScanSession(
    targets: @[],
    currentTarget: 0,
    startTime: cpuTime()
  )
  
  for i, domain in domains:
    showProgress(i + 1, domains.len, "扫描: " & domain)
    let target = await performFullScan(domain)
    currentSession.targets.add(target)
    echo ""  # 换行
  
  let endTime = cpuTime()
  
  # 显示批量扫描结果
  echo ""
  stdout.styledWrite(fgGreen, styleBright, "=== 批量扫描完成 ===")
  echo ""
  stdout.styledWrite(fgWhite, "总计扫描: ", fgYellow, $domains.len, " 个域名")
  echo ""
  stdout.styledWrite(fgWhite, "总耗时: ", fgYellow, $(endTime - currentSession.startTime), " 秒")
  echo ""
  
  # 显示摘要统计
  var cdnCount = 0
  var realIPCount = 0
  
  for target in currentSession.targets:
    if target.hasCDN:
      cdnCount += 1
    if target.realIP != "":
      realIPCount += 1
  
  stdout.styledWrite(fgWhite, "CDN检出率: ", fgRed, $cdnCount, "/", $domains.len)
  echo ""
  stdout.styledWrite(fgWhite, "真实IP发现: ", fgGreen, $realIPCount, "/", $cdnCount)
  echo ""

proc showHelp() =
  echo ""
  stdout.styledWrite(fgCyan, styleBright, "=== 开天 使用帮助 ===")
  echo ""
  stdout.styledWrite(fgWhite, "功能说明:")
  echo ""
  stdout.styledWrite(fgYellow, "1. CDN检测")
  stdout.styledWrite(fgWhite, " - 通过多种方式检测目标是否使用CDN")
  echo ""
  stdout.styledWrite(fgYellow, "2. DNS历史查询")
  stdout.styledWrite(fgWhite, " - 查询DNS历史记录找到真实IP")
  echo ""
  stdout.styledWrite(fgYellow, "3. 信息收集")
  stdout.styledWrite(fgWhite, " - 证书、备案、搜索引擎、WHOIS等")
  echo ""
  stdout.styledWrite(fgYellow, "4. 子域名收集")
  stdout.styledWrite(fgWhite, " - 被动收集+主动爆破")
  echo ""
  stdout.styledWrite(fgYellow, "5. Web探测")
  stdout.styledWrite(fgWhite, " - 爬虫、JS分析、API测试")
  echo ""
  stdout.styledWrite(fgYellow, "6. 漏洞扫描")
  stdout.styledWrite(fgWhite, " - 指纹识别、N-day检测")
  echo ""

proc startInteractiveMode*(): Future[void] {.async.} =
  ## 启动交互模式
  while true:
    showMenu()
    let choice = readInput()
    
    case choice:
      of "1":
        await singleDomainScan()
      of "2":
        await batchDomainScan()
      of "3":
        echo "扫描历史功能开发中..."
      of "4":
        echo "配置设置功能开发中..."
      of "5":
        showHelp()
      of "0":
        logInfo("感谢使用开天工具，再见！")
        break
      else:
        logError("无效选择，请重新输入")
    
    echo ""
    stdout.styledWrite(fgBlack, "按回车键继续...")
    discard readInput()
