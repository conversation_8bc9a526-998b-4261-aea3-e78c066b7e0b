##[
  开天 (KaiTian) - 全自动渗透测试工具
  
  功能特性:
  - CDN检测与绕过
  - DNS历史记录查询
  - 全面信息收集
  - 子域名收集
  - Web探测与漏洞扫描
  - 反检测与WAF绕过
  - 实时交互式界面
]##

import asyncdispatch, os, strutils, sequtils
import core/[config, logger, banner]
import modules/[
  cdn_detector,
  dns_history,
  info_collector,
  subdomain_collector,
  web_scanner,
  fingerprint_scanner,
  vuln_scanner,
  evasion
]
import ui/interactive

proc main() {.async.} =
  # 显示Banner
  showBanner()
  
  # 初始化配置
  let config = loadConfig()
  initLogger(config.logLevel)
  
  # 启动交互式界面
  await startInteractiveMode()

when isMainModule:
  waitFor main()
