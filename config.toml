[basic]
log_level = "INFO"
output_dir = "./output"
max_concurrency = 50
request_timeout = 10

[evasion]
user_agents = [
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
]
proxy_list = []
request_delay_min = 1
request_delay_max = 3

[apis]
dns_history_apis = [
  "https://api.hackertarget.com/hostsearch/?q=",
  "https://crt.sh/?q=%25.{domain}&output=json"
]

[scanning]
subdomain_wordlist = "./wordlists/subdomains.txt"
directory_wordlist = "./wordlists/directories.txt"
port_range = "1-65535"

[output]
enable_real_time_output = true
report_format = "json"
