#!/bin/bash

# 开天工具演示脚本
# Demo script for KaiTian penetration testing tool

echo "========================================"
echo "    开天 (KaiTian) 工具演示"
echo "    Automated Penetration Testing Demo"
echo "========================================"
echo ""

# 检查是否已编译
if [ ! -f "src/kaitian" ]; then
    echo "🔧 正在编译开天工具..."
    echo "Compiling KaiTian tool..."
    nim c -d:ssl src/kaitian.nim
    
    if [ $? -eq 0 ]; then
        echo "✅ 编译成功！"
        echo "Compilation successful!"
    else
        echo "❌ 编译失败，请检查Nim环境"
        echo "Compilation failed, please check Nim environment"
        exit 1
    fi
fi

echo ""
echo "🚀 启动开天工具..."
echo "Starting KaiTian tool..."
echo ""
echo "💡 使用提示:"
echo "   1. 选择 '1' 进行单域名扫描"
echo "   2. 选择 '5' 查看帮助信息"
echo "   3. 选择 '0' 退出程序"
echo ""
echo "💡 Usage tips:"
echo "   1. Select '1' for single domain scan"
echo "   2. Select '5' for help information"
echo "   3. Select '0' to exit"
echo ""

# 启动工具
./src/kaitian
