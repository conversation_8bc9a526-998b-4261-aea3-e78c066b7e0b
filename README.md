# 开天 (KaiTian) - 全自动渗透测试工具

<div align="center">

```
██╗  ██╗ █████╗ ██╗████████╗██╗ █████╗ ███╗   ██╗
██║ ██╔╝██╔══██╗██║╚══██╔══╝██║██╔══██╗████╗  ██║
█████╔╝ ███████║██║   ██║   ██║███████║██╔██╗ ██║
██╔═██╗ ██╔══██║██║   ██║   ██║██╔══██║██║╚██╗██║
██║  ██╗██║  ██║██║   ██║   ██║██║  ██║██║ ╚████║
╚═╝  ╚═╝╚═╝  ╚═╝╚═╝   ╚═╝   ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝
```

**全自动渗透测试框架 | Automated Penetration Testing Framework**

[![Nim](https://img.shields.io/badge/Nim-2.0+-yellow.svg)](https://nim-lang.org/)
[![License](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Linux%20%7C%20macOS%20%7C%20Windows-lightgrey.svg)]()

</div>

## 🚀 功能特性

### 核心功能
- **🔍 CDN检测与绕过** - 多种方式检测CDN并尝试找到真实IP
- **📜 DNS历史记录查询** - 通过多个免费API查询历史解析记录
- **🔎 全面信息收集** - 证书查询、ICP备案、搜索引擎、WHOIS等
- **🌐 子域名收集** - 被动收集(证书透明度) + 主动收集(字典爆破)
- **🕷️ Web探测** - 爬虫、JS文件分析、API模糊测试
- **🔧 指纹识别** - 服务指纹识别、端口扫描、漏洞检测
- **🛡️ 反检测技术** - WAF绕过、流量隐秘、User-Agent轮换

### 技术特性
- **⚡ 高性能** - 基于Nim语言，编译为原生代码
- **🔄 异步并发** - 高并发异步处理，提升扫描效率
- **🎯 实时输出** - 扫描结果实时回显，进度可视化
- **🖥️ 交互界面** - 友好的命令行交互界面
- **🌍 跨平台** - 支持Linux、macOS、Windows
- **📊 结果导出** - 支持多种格式的报告生成

## 📦 安装使用

### 环境要求
- Nim 1.6.0+
- OpenSSL (用于HTTPS支持)

### 编译安装
```bash
# 克隆项目
git clone https://github.com/kaitian-security/kaitian.git
cd kaitian

# 编译 (启用SSL支持)
nim c -d:ssl src/kaitian.nim

# 运行
./src/kaitian
```

### 快速开始
1. 启动工具后选择 `1. 单域名扫描`
2. 输入目标域名，如 `example.com`
3. 工具将自动执行完整的扫描流程
4. 查看实时输出的扫描结果

## 🔧 扫描流程

### 自动化扫描流程
```
输入域名 → CDN检测 → DNS历史查询 → 信息收集 → 子域名收集 → Web探测 → 漏洞扫描 → 生成报告
```

### 详细步骤
1. **CDN检测** - 通过HTTP头、CNAME记录、多地点ping等方式检测CDN
2. **真实IP发现** - 如果检测到CDN，通过DNS历史记录查找真实IP
3. **信息收集** - 收集证书信息、备案信息、WHOIS等
4. **子域名收集** - 被动收集和主动爆破相结合
5. **Web探测** - 爬虫分析、JS文件扫描、敏感信息提取
6. **指纹识别** - 识别Web应用、中间件、数据库等
7. **漏洞扫描** - 针对性的N-day漏洞检测

## 🛠️ 项目结构

```
KaiTian/
├── src/
│   ├── core/                 # 核心模块
│   │   ├── config.nim       # 配置管理
│   │   ├── logger.nim       # 日志系统
│   │   └── banner.nim       # 界面显示
│   ├── modules/              # 功能模块
│   │   ├── cdn_detector.nim  # CDN检测
│   │   ├── dns_history.nim   # DNS历史查询
│   │   ├── info_collector.nim # 信息收集
│   │   ├── subdomain_collector.nim # 子域名收集
│   │   ├── web_scanner.nim   # Web扫描
│   │   ├── fingerprint_scanner.nim # 指纹识别
│   │   ├── vuln_scanner.nim  # 漏洞扫描
│   │   └── evasion.nim       # 反检测
│   ├── ui/
│   │   └── interactive.nim   # 交互界面
│   └── kaitian.nim          # 主程序
├── wordlists/               # 字典文件
├── config.toml             # 配置文件
└── kaitian.nimble          # 项目配置
```

## 🎯 使用示例

### 单域名扫描
```bash
# 启动工具
./src/kaitian

# 选择 1 进行单域名扫描
# 输入目标域名: example.com
# 等待扫描完成并查看结果
```

### 批量域名扫描
```bash
# 选择 2 进行批量扫描
# 输入域名列表: example.com,test.com,demo.com
# 工具将依次扫描所有域名
```

## ⚠️ 免责声明

本工具仅供安全研究和授权的渗透测试使用。使用者应当：

- 仅在获得明确授权的目标上使用
- 遵守当地法律法规
- 不得用于非法用途
- 承担使用本工具的一切责任

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📞 联系方式

- 项目主页: https://github.com/kaitian-security/kaitian
- 问题反馈: https://github.com/kaitian-security/kaitian/issues

---

<div align="center">
<b>⭐ 如果这个项目对你有帮助，请给个Star支持一下！</b>
</div>
